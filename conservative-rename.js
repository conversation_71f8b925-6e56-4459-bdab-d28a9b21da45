#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 保守的变量名重命名工具
 * 只重命名明确识别的混淆变量，避免破坏代码结构
 */

class ConservativeRenamer {
  constructor() {
    // 只包含高置信度的重命名映射
    this.safeMappings = {
      // 明确的混淆模式 - 这些是webpack/打包工具生成的
      'MLt': 'ObjectCreate',
      'LW': 'defineProperty', 
      'FLt': 'getOwnPropertyDescriptor',
      'YLt': 'getOwnPropertyNames',
      'NLt': 'getPrototypeOf',
      'PLt': 'hasOwnProperty',
      
      // 高频混淆变量
      'Y1W': 'moduleLoader',
      'O2p': 'exportHelper', 
      'L9p': 'requireHelper',
      'P0d': 'propertyDefiner',
      'q4L': 'scopeManager',
      
      // 功能相关的明确变量
      'sha1': 'sha1Hash',
      'sha512': 'sha512Hash',
      'md5': 'md5Hash',
      'asn1': 'asn1Parser',
      'HTML': 'htmlContent',
      'URL': 'urlHelper',
      'DB': 'database',
      'INT32': 'int32Type',
      'ONE': 'constantOne',
      'OMMM': 'objectMarker'
    };
  }

  processFile(filePath, analysisPath) {
    console.log(`\n🛡️  保守重命名: ${path.basename(filePath)}`);
    
    // 读取分析结果
    const analysis = JSON.parse(fs.readFileSync(analysisPath, 'utf8'));
    
    // 读取源文件
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 生成安全的重命名映射
    const safeRenames = this.generateSafeRenames(analysis);
    
    // 应用重命名
    const renamedContent = this.applySafeRenames(content, safeRenames);
    
    // 生成报告
    const report = this.generateReport(safeRenames, analysis);
    
    // 保存结果
    this.saveResults(renamedContent, report, filePath);
    
    return { content: renamedContent, report, safeRenames };
  }

  generateSafeRenames(analysis) {
    const safeRenames = new Map();
    
    // 只处理在安全映射中的变量
    analysis.topObfuscated.forEach(variable => {
      const oldName = variable.name;
      if (this.safeMappings[oldName]) {
        safeRenames.set(oldName, {
          newName: this.safeMappings[oldName],
          oldName,
          frequency: variable.count,
          confidence: 0.95, // 高置信度
          reason: '预定义安全映射'
        });
      }
    });

    // 添加一些明显的模式
    analysis.topObfuscated.forEach(variable => {
      const oldName = variable.name;
      
      // 跳过已处理的
      if (safeRenames.has(oldName)) return;
      
      // 只处理特定模式的变量名
      if (this.isSafeToRename(oldName, variable)) {
        const newName = this.generateSafeName(oldName, variable);
        if (newName) {
          safeRenames.set(oldName, {
            newName,
            oldName,
            frequency: variable.count,
            confidence: 0.8,
            reason: '安全模式匹配'
          });
        }
      }
    });

    return safeRenames;
  }

  isSafeToRename(name, variable) {
    // 只重命名明显的混淆模式
    const safePatterns = [
      /^[A-Z]{3,4}[a-z]?$/, // 如: MLt, FLt, YLt
      /^[A-Z]\d+[A-Z]$/,    // 如: Y1W, O2p, L9p
      /^[a-z]\d+[A-Z]$/,    // 如: q4L
      /^[A-Z]{2,4}\d*$/,    // 如: DB, HTML, INT32
    ];
    
    return safePatterns.some(pattern => pattern.test(name)) && 
           name.length >= 2 && // 至少2个字符
           variable.count >= 10; // 使用频率足够高
  }

  generateSafeName(oldName, variable) {
    // 基于模式生成安全的名称
    if (/^[A-Z]{3,4}[a-z]?$/.test(oldName)) {
      return `helper_${oldName.toLowerCase()}`;
    }
    
    if (/^[A-Z]\d+[A-Z]$/.test(oldName)) {
      return `module_${oldName.toLowerCase()}`;
    }
    
    if (/^[a-z]\d+[A-Z]$/.test(oldName)) {
      return `util_${oldName.toLowerCase()}`;
    }
    
    if (/^[A-Z]{2,4}\d*$/.test(oldName) && oldName.length <= 6) {
      return `const_${oldName.toLowerCase()}`;
    }
    
    return null; // 不确定的情况不重命名
  }

  applySafeRenames(content, safeRenames) {
    let renamedContent = content;
    
    // 按长度排序，先处理长的变量名
    const sortedRenames = Array.from(safeRenames.entries())
      .sort(([a], [b]) => b.length - a.length);
    
    sortedRenames.forEach(([oldName, renameInfo]) => {
      // 使用精确的词边界匹配
      const regex = new RegExp(`\\b${this.escapeRegex(oldName)}\\b`, 'g');
      
      // 计算替换次数以验证
      const matches = content.match(regex);
      const expectedCount = matches ? matches.length : 0;
      
      if (expectedCount > 0) {
        renamedContent = renamedContent.replace(regex, renameInfo.newName);
        console.log(`   ${oldName} → ${renameInfo.newName} (${expectedCount}次)`);
      }
    });
    
    return renamedContent;
  }

  escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  generateReport(safeRenames, analysis) {
    const appliedRenames = Array.from(safeRenames.values())
      .sort((a, b) => b.frequency - a.frequency);

    return {
      summary: {
        totalAnalyzed: analysis.summary.totalVariables,
        candidatesFound: analysis.topObfuscated.length,
        safeRenames: appliedRenames.length,
        averageConfidence: appliedRenames.reduce((sum, r) => sum + r.confidence, 0) / appliedRenames.length || 0,
        preservedSafety: true
      },
      appliedRenames,
      skippedVariables: analysis.topObfuscated.length - appliedRenames.length,
      recommendations: [
        {
          type: '安全性保证',
          description: '只应用了高置信度的重命名，保证代码功能完整性',
          priority: 'info'
        },
        {
          type: '进一步优化',
          description: `还有${analysis.topObfuscated.length - appliedRenames.length}个变量可以手动分析`,
          priority: 'low'
        }
      ]
    };
  }

  saveResults(content, report, filePath) {
    // 保存重命名后的文件
    const safePath = filePath.replace('.js', '-safe-renamed.js');
    fs.writeFileSync(safePath, content, 'utf8');
    
    // 保存报告
    const reportPath = filePath.replace('.js', '-safe-rename-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
    
    // 生成文本报告
    const textReport = this.generateTextReport(report);
    const textReportPath = filePath.replace('.js', '-safe-rename-report.txt');
    fs.writeFileSync(textReportPath, textReport, 'utf8');
    
    console.log(`✅ 安全重命名完成:`);
    console.log(`   输出文件: ${path.basename(safePath)}`);
    console.log(`   JSON报告: ${path.basename(reportPath)}`);
    console.log(`   文本报告: ${path.basename(textReportPath)}`);
    console.log(`📊 统计: 安全重命名了${report.summary.safeRenames}个变量`);
  }

  generateTextReport(report) {
    let text = `安全重命名报告\n`;
    text += `==================\n\n`;
    
    text += `📈 处理统计:\n`;
    text += `- 分析的变量总数: ${report.summary.totalAnalyzed}\n`;
    text += `- 混淆变量候选: ${report.summary.candidatesFound}\n`;
    text += `- 安全重命名数量: ${report.summary.safeRenames}\n`;
    text += `- 平均置信度: ${(report.summary.averageConfidence * 100).toFixed(1)}%\n`;
    text += `- 跳过的变量: ${report.skippedVariables}\n\n`;
    
    if (report.appliedRenames.length > 0) {
      text += `✅ 已应用的安全重命名:\n`;
      report.appliedRenames.forEach((rename, i) => {
        text += `${i+1}. ${rename.oldName} → ${rename.newName} (${rename.frequency}次, ${(rename.confidence*100).toFixed(1)}%)\n`;
        text += `   原因: ${rename.reason}\n`;
      });
      text += `\n`;
    }
    
    text += `💡 建议:\n`;
    report.recommendations.forEach((rec, i) => {
      text += `${i+1}. [${rec.priority.toUpperCase()}] ${rec.type}: ${rec.description}\n`;
    });
    
    return text;
  }
}

// 主程序
if (require.main === module) {
  const args = process.argv.slice(2);
  if (args.length < 2) {
    console.log('用法: node conservative-rename.js <源文件路径> <分析文件路径>');
    console.log('例如: node conservative-rename.js 513/extension-improved.js 513/extension-improved-variable-analysis.json');
    process.exit(1);
  }
  
  const [filePath, analysisPath] = args;
  
  if (!fs.existsSync(filePath)) {
    console.error(`❌ 源文件不存在: ${filePath}`);
    process.exit(1);
  }
  
  if (!fs.existsSync(analysisPath)) {
    console.error(`❌ 分析文件不存在: ${analysisPath}`);
    process.exit(1);
  }
  
  try {
    const renamer = new ConservativeRenamer();
    renamer.processFile(filePath, analysisPath);
  } catch (error) {
    console.error('❌ 重命名过程中出错:', error.message);
    process.exit(1);
  }
}

module.exports = ConservativeRenamer;
