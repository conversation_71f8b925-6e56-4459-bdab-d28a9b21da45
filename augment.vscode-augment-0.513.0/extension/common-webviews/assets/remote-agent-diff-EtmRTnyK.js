var ki=Object.defineProperty;var sn=r=>{throw TypeError(r)};var Ai=(r,t,e)=>t in r?ki(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var gt=(r,t,e)=>Ai(r,typeof t!="symbol"?t+"":t,e),bi=(r,t,e)=>t.has(r)||sn("Cannot "+e);var rn=(r,t,e)=>t.has(r)?sn("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(r):t.set(r,e);var De=(r,t,e)=>(bi(r,t,"access private method"),e);import{C as Kt,S as tt,i as et,s as K,D as b,d as v,a0 as Ft,f as h,h as R,n as st,k as m,a5 as bt,Y as N,Z as rt,E as z,F as L,w as f,u as g,G as M,V as I,P as Nt,ae as Zs,Q as Qt,ab as We,T as ze,ac as Gt,ag as Le,t as j,v as W,O as Hs,ak as Rt,a8 as Mt,N as vt,az as we,am as Ie,aj as te,a6 as ee,a7 as ne,ay as js,X as $t,av as qt,ah as Ei,ai as on,b as Ws,J as _t,a as Qs,aa as ve,K as Bt,L as zt,M as Lt,j as Gs,at as ln,au as _i,W as Bi,al as Me}from"./SpinnerAugment-uKUHz-bK.js";import"./design-system-init-DDX-Gvwz.js";import{g as Fe,p as an,a as zi}from"./index-CagAjuOI.js";import"./design-system-init-CCbpeEq3.js";import{W as It,I as pe,e as dt,u as Js,o as Ys,h as Xs,g as Li}from"./IconButtonAugment-CQzh_Hae.js";import{M as Mi}from"./message-broker-DdVtH9Vr.js";import{s as cn}from"./index-GYuo8qik.js";import{c as ye,p as Ri,g as Zt,M as Oi,O as Qe,P as fe,a as Ni,i as qi,b as Si,C as Ti,E as Pi}from"./diff-utils-D5NvkEWZ.js";import{C as Ks,a as ti,T as ei,b as Ii}from"./CollapseButtonAugment-ffrJmKr6.js";import{a as Ge,g as Je,b as ni,S as Ui,M as Vi}from"./index-D0JCd9Au.js";import{V as $e}from"./VSCodeCodicon-n5HCoiGq.js";import{d as Zi}from"./index-BBwB6w04.js";import{T as Vt,a as re,d as Hi,C as ji}from"./CardAugment-BqjOeIg4.js";import{B as Tt}from"./ButtonAugment-D5QDitBR.js";import{M as Re}from"./MaterialIcon-ggitH03G.js";import{i as si,b as jt,c as ii,d as ke,n as ri,a as se,g as ot,M as oi}from"./file-paths-CAgP5Fvb.js";import{F as Wi}from"./types-DDm27S8B.js";import{L as li}from"./LanguageIcon-DGbwX4zn.js";import{R as Qi}from"./ra-diff-ops-model-DLmlSKam.js";import{A as Gi}from"./async-messaging-D4p6YcQf.js";import{E as ai}from"./exclamation-triangle-Ba2-vrJ4.js";import"./toggleHighContrast-BSg_W9Au.js";import{F as Ji}from"./Filespan-DNGY17t7.js";import{M as Yi}from"./ModalAugment-CtjgW1F_.js";import"./index-Bcx5x-t6.js";import"./preload-helper-Dv6uf1Os.js";class Ae{constructor(t){gt(this,"_opts",null);gt(this,"_subscribers",new Set);this._asyncMsgSender=t}subscribe(t){return this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}}notifySubscribers(){this._subscribers.forEach(t=>t(this))}get opts(){return this._opts}updateOpts(t){this._opts=t,this.notifySubscribers()}async onPanelLoaded(){try{this.updateOpts(null);const t=await this._asyncMsgSender.send({type:It.remoteAgentDiffPanelLoaded});this.updateOpts(t.data)}catch(t){console.error("Failed to load diff panel:",t),this.updateOpts(null)}}handleMessageFromExtension(t){const e=t.data;return!(!e||!e.type)&&e.type===It.remoteAgentDiffPanelSetOpts&&(this.updateOpts(e.data),!0)}}gt(Ae,"key","remoteAgentDiffModel");class oe{constructor(t){gt(this,"_applyingFilePaths",Kt([]));gt(this,"_appliedFilePaths",Kt([]));this._asyncMsgSender=t}get applyingFilePaths(){let t=[];return this._applyingFilePaths.subscribe(e=>{t=e})(),t}get appliedFilePaths(){let t=[];return this._appliedFilePaths.subscribe(e=>{t=e})(),t}async getDiffExplanation(t,e,n=3e4){try{return(await this._asyncMsgSender.send({type:It.diffExplanationRequest,data:{changedFiles:t,apikey:e}},n)).data.explanation}catch(s){return console.error("Failed to get diff explanation:",s),[]}}async groupChanges(t,e=!1,n){try{return(await this._asyncMsgSender.send({type:It.diffGroupChangesRequest,data:{changedFiles:t,changesById:e,apikey:n}})).data.groupedChanges}catch(s){return console.error("Failed to group changes:",s),[]}}async getDescriptions(t,e){try{const n=await this._asyncMsgSender.send({type:It.diffDescriptionsRequest,data:{groupedChanges:t,apikey:e}},1e5);return{explanation:n.data.explanation,error:n.data.error}}catch(n){return console.error("Failed to get descriptions:",n),{explanation:[],error:`Failed to get descriptions: ${n instanceof Error?n.message:String(n)}`}}}async canApplyChanges(){try{return(await this._asyncMsgSender.send({type:It.canApplyChangesRequest},1e4)).data}catch(t){return console.error("Failed to check if can apply changes:",t),{canApply:!1,hasUnstagedChanges:!1,error:`Failed to check if can apply changes: ${t instanceof Error?t.message:String(t)}`}}}async applyChanges(t,e,n){this._applyingFilePaths.update(s=>[...s.filter(i=>i!==t),t]);try{const s=await this._asyncMsgSender.send({type:It.applyChangesRequest,data:{path:t,originalCode:e,newCode:n}},3e4),{success:i,hasConflicts:o,error:l}=s.data;return i?this._appliedFilePaths.update(a=>[...a.filter(c=>c!==t),t]):l&&console.error("Failed to apply changes:",l),{success:i,hasConflicts:o,error:l}}catch(s){return console.error("applyChanges error",s),{success:!1,error:`Error: ${s instanceof Error?s.message:String(s)}`}}finally{this._applyingFilePaths.update(s=>s.filter(i=>i!==t))}}async previewApplyChanges(t,e,n){try{return(await this._asyncMsgSender.send({type:It.previewApplyChangesRequest,data:{path:t,originalCode:e,newCode:n}},3e4)).data}catch(s){return console.error("previewApplyChanges error",s),{mergedContent:"",hasConflicts:!1,error:`Error: ${s instanceof Error?s.message:String(s)}`}}}async openFile(t){try{const e=await this._asyncMsgSender.send({type:It.openFileRequest,data:{path:t}},1e4);return e.data.success||console.error("Failed to open file:",e.data.error),e.data.success}catch(e){console.error("openFile error",e)}return!1}async stashUnstagedChanges(){try{const t=await this._asyncMsgSender.send({type:It.stashUnstagedChangesRequest},1e4);return t.data.success||console.error("Failed to stash unstaged changes:",t.data.error),t.data.success}catch(t){console.error("stashUnstagedChanges error",t)}return!1}async reportApplyChangesEvent(){await this._asyncMsgSender.send({type:It.reportAgentChangesApplied})}}gt(oe,"key","remoteAgentsDiffOpsModel");function un(r,t,e,n,s={}){const{context:i=3,generateId:o=!0}=s,l=ye(r,t,e,n,"","",{context:i}),a=t||r;let c;return o?c=`${Zt(a)}-${Zt(e+n)}`:c=Math.random().toString(36).substring(2,15),{id:c,path:a,diff:l,originalCode:e,modifiedCode:n}}function Ue(r){const t=r.split(`
`);return{additions:t.filter(e=>e.startsWith("+")&&!e.startsWith("+++")).length,deletions:t.filter(e=>e.startsWith("-")&&!e.startsWith("---")).length}}function ci(r){return!r.originalCode||r.originalCode.trim()===""}function ui(r){return!r.modifiedCode||r.modifiedCode.trim()===""}class Xi{static generateDiff(t,e,n,s){return un(t,e,n,s)}static generateDiffs(t){return function(e,n={}){return e.map(s=>un(s.oldPath,s.newPath,s.oldContent,s.newContent,n))}(t)}static getDiffStats(t){return Ue(t)}static getDiffObjectStats(t){return Ue(t.diff)}static isNewFile(t){return ci(t)}static isDeletedFile(t){return ui(t)}}function Ki(r){let t;return{c(){t=N(r[1])},m(e,n){h(e,t,n)},p(e,n){2&n&&rt(t,e[1])},d(e){e&&m(t)}}}function tr(r){let t;return{c(){t=N(r[1])},m(e,n){h(e,t,n)},p(e,n){2&n&&rt(t,e[1])},d(e){e&&m(t)}}}function er(r){let t,e,n;function s(l,a){return l[2]?tr:Ki}let i=s(r),o=i(r);return{c(){t=b("span"),e=b("code"),o.c(),v(e,"class","markdown-codespan svelte-11ta4gi"),v(e,"style",n=r[2]?`background-color: ${r[1]}; color: ${r[3]?"white":"black"}`:""),Ft(e,"markdown-string",r[4])},m(l,a){h(l,t,a),R(t,e),o.m(e,null),r[6](t)},p(l,[a]){i===(i=s(l))&&o?o.p(l,a):(o.d(1),o=i(l),o&&(o.c(),o.m(e,null))),14&a&&n!==(n=l[2]?`background-color: ${l[1]}; color: ${l[3]?"white":"black"}`:"")&&v(e,"style",n),16&a&&Ft(e,"markdown-string",l[4])},i:st,o:st,d(l){l&&m(t),o.d(),r[6](null)}}}function nr(r,t,e){let n,s,i,o,{token:l}=t,{element:a}=t;return r.$$set=c=>{"token"in c&&e(5,l=c.token),"element"in c&&e(0,a=c.element)},r.$$.update=()=>{32&r.$$.dirty&&e(1,n=l.raw.slice(1,l.raw.length-1)),2&r.$$.dirty&&e(4,s=n.startsWith('"')),2&r.$$.dirty&&e(2,i=/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(n)),6&r.$$.dirty&&e(3,o=i&&function(c){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(c))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let u,d,p;return c.length===4?(u=parseInt(c.charAt(1),16),d=parseInt(c.charAt(2),16),p=parseInt(c.charAt(3),16),u*=17,d*=17,p*=17):(u=parseInt(c.slice(1,3),16),d=parseInt(c.slice(3,5),16),p=parseInt(c.slice(5,7),16)),.299*u+.587*d+.114*p<130}(n))},[a,n,i,o,s,l,function(c){bt[c?"unshift":"push"](()=>{a=c,e(0,a)})}]}let sr=class extends tt{constructor(r){super(),et(this,r,nr,er,K,{token:5,element:0})}};function ir(r){let t,e;return t=new Oi({props:{markdown:r[1](r[0]),renderers:r[2]}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,[s]){const i={};1&s&&(i.markdown=n[1](n[0])),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function rr(r,t,e){let{markdown:n}=t;const s={codespan:sr};return r.$$set=i=>{"markdown"in i&&e(0,n=i.markdown)},[n,i=>i.replace(/`?#[0-9a-fA-F]{3,6}`?/g,o=>o.startsWith("`")?o:`\`${o}\``),s]}let be=class extends tt{constructor(r){super(),et(this,r,rr,ir,K,{markdown:0})}};function dn(r,t){return`${r}:${t}`}const or=(r,t)=>{let e=null,n=null,s=null,i=!1;function o(){s&&cancelAnimationFrame(s),s=requestAnimationFrame(()=>{const{path:u,onCollapseStateChange:d}=t;if(i)return void(s=null);const p=Array.from(document.querySelectorAll(`[data-description-id^="${u}:"]`));let C=!1;for(const x of p)if(x!==r&&l(r,x)){C=!0;break}C&&(i=!0),d&&d(C),s=null})}function l(u,d){const p=u.getBoundingClientRect(),C=d.getBoundingClientRect();return!(p.bottom<=C.top||C.bottom<=p.top)}function a(){c(),e=new MutationObserver(()=>{o()});const u=r.closest(".descriptions")||document.body;e.observe(u,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["style","data-description-id"]}),window.ResizeObserver&&(n=new ResizeObserver(()=>{o()}),n.observe(r)),window.addEventListener("resize",o),window.addEventListener("scroll",o)}function c(){e&&(e.disconnect(),e=null),n&&(n.disconnect(),n=null),s&&(cancelAnimationFrame(s),s=null),window.removeEventListener("resize",o),window.removeEventListener("scroll",o)}return document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>{a(),o()}):requestAnimationFrame(()=>{a(),o()}),{update:u=>{t=u,i=!1,o()},destroy:c}};function lr(r){let t,e,n,s,i,o,l,a,c,u,d,p,C;return n=new be({props:{markdown:r[0].text}}),o=new be({props:{markdown:r[7]}}),{c(){t=b("div"),e=b("div"),z(n.$$.fragment),s=I(),i=b("div"),z(o.$$.fragment),l=I(),a=b("div"),a.textContent="hover to expand",v(e,"class","c-diff-description__content svelte-wweiw1"),v(a,"class","c-diff-description__expand-hint svelte-wweiw1"),v(i,"class","c-diff-description__truncated-content svelte-wweiw1"),v(t,"class","c-diff-description svelte-wweiw1"),Nt(t,"top",r[1]+"px"),v(t,"data-description-id",c=dn(r[2],r[3])),v(t,"role","region"),v(t,"aria-label","Code diff description"),Ft(t,"c-diff-description__collapsed",r[6]&&!r[5]),Ft(t,"c-diff-description__hovered",r[5])},m(x,$){h(x,t,$),R(t,e),L(n,e,null),R(t,s),R(t,i),L(o,i,null),R(i,l),R(i,a),r[10](t),d=!0,p||(C=[Zs(u=or.call(null,t,{path:r[2],onCollapseStateChange:r[11]})),Qt(t,"mouseenter",r[12]),Qt(t,"mouseleave",r[13])],p=!0)},p(x,[$]){const k={};1&$&&(k.markdown=x[0].text),n.$set(k);const _={};128&$&&(_.markdown=x[7]),o.$set(_),(!d||2&$)&&Nt(t,"top",x[1]+"px"),(!d||12&$&&c!==(c=dn(x[2],x[3])))&&v(t,"data-description-id",c),u&&We(u.update)&&68&$&&u.update.call(null,{path:x[2],onCollapseStateChange:x[11]}),(!d||96&$)&&Ft(t,"c-diff-description__collapsed",x[6]&&!x[5]),(!d||32&$)&&Ft(t,"c-diff-description__hovered",x[5])},i(x){d||(f(n.$$.fragment,x),f(o.$$.fragment,x),d=!0)},o(x){g(n.$$.fragment,x),g(o.$$.fragment,x),d=!1},d(x){x&&m(t),M(n),M(o),r[10](null),p=!1,ze(C)}}}function ar(r){const t=document.createElement("canvas").getContext("2d");return t?t.measureText(r).width:8*r.length}function cr(r,t,e){let n,s,{description:i}=t,{position:o}=t,{fileId:l}=t,{index:a}=t,c=!1,u=!1,d=0;const p=Zi($=>{e(5,c=$)},100);function C(){if(s){const $=s.getBoundingClientRect();e(9,d=$.width-128)}}let x=null;return Gt(()=>{s&&typeof ResizeObserver<"u"&&(x=new ResizeObserver(()=>{C()}),x.observe(s),C())}),Le(()=>{x&&x.disconnect()}),r.$$set=$=>{"description"in $&&e(0,i=$.description),"position"in $&&e(1,o=$.position),"fileId"in $&&e(2,l=$.fileId),"index"in $&&e(3,a=$.index)},r.$$.update=()=>{16&r.$$.dirty&&s&&C(),513&r.$$.dirty&&e(7,n=(()=>{const $=i.text.split(`
`)[0].split(" ");let k="";if(d<=0)for(const _ of $){const B=k+(k?" ":"")+_;if(B.length>30)break;k=B}else for(const _ of $){const B=k+(k?" ":"")+_;if(ar(B+"...")>d)break;k=B}return k+"..."})())},[i,o,l,a,s,c,u,n,p,d,function($){bt[$?"unshift":"push"](()=>{s=$,e(4,s)})},$=>e(6,u=$),$=>{p.cancel(),e(5,c=!0),$.stopPropagation()},$=>{p(!1),$.stopPropagation()}]}let ur=class extends tt{constructor(r){super(),et(this,r,cr,lr,K,{description:0,position:1,fileId:2,index:3})}};function pn(r,t,e){const n=r.slice();return n[48]=t[e],n[50]=e,n}function fn(r){let t,e,n,s,i;e=new pe({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[fr]},$$scope:{ctx:r}}}),e.$on("click",r[25]);let o=dt(r[1]),l=[];for(let c=0;c<o.length;c+=1)l[c]=$n(pn(r,o,c));const a=c=>g(l[c],1,1,()=>{l[c]=null});return{c(){t=b("div"),z(e.$$.fragment),n=I(),s=b("div");for(let c=0;c<l.length;c+=1)l[c].c();v(t,"class","toggle-button svelte-1r29xbx"),v(s,"class","descriptions svelte-1r29xbx"),Nt(s,"transform","translateY("+-r[4]+"px)")},m(c,u){h(c,t,u),L(e,t,null),h(c,n,u),h(c,s,u);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(s,null);i=!0},p(c,u){const d={};if(1&u[0]|1048576&u[1]&&(d.$$scope={dirty:u,ctx:c}),e.$set(d),1570&u[0]){let p;for(o=dt(c[1]),p=0;p<o.length;p+=1){const C=pn(c,o,p);l[p]?(l[p].p(C,u),f(l[p],1)):(l[p]=$n(C),l[p].c(),f(l[p],1),l[p].m(s,null))}for(j(),p=o.length;p<l.length;p+=1)a(p);W()}(!i||16&u[0])&&Nt(s,"transform","translateY("+-c[4]+"px)")},i(c){if(!i){f(e.$$.fragment,c);for(let u=0;u<o.length;u+=1)f(l[u]);i=!0}},o(c){g(e.$$.fragment,c),l=l.filter(Boolean);for(let u=0;u<l.length;u+=1)g(l[u]);i=!1},d(c){c&&(m(t),m(n),m(s)),M(e),Mt(l,c)}}}function dr(r){let t,e;return t=new $e({props:{icon:"book"}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function pr(r){let t,e;return t=new $e({props:{icon:"x"}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function fr(r){let t,e,n,s;const i=[pr,dr],o=[];function l(a,c){return a[0]?0:1}return t=l(r),e=o[t]=i[t](r),{c(){e.c(),n=vt()},m(a,c){o[t].m(a,c),h(a,n,c),s=!0},p(a,c){let u=t;t=l(a),t!==u&&(j(),g(o[u],1,1,()=>{o[u]=null}),W(),e=o[t],e||(e=o[t]=i[t](a),e.c()),f(e,1),e.m(n.parentNode,n))},i(a){s||(f(e),s=!0)},o(a){g(e),s=!1},d(a){a&&m(n),o[t].d(a)}}}function $n(r){let t,e;return t=new ur({props:{description:r[48],position:r[5][r[50]]||r[9](r[48]),fileId:r[10],index:r[50]}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};2&s[0]&&(i.description=n[48]),34&s[0]&&(i.position=n[5][n[50]]||n[9](n[48])),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function $r(r){let t,e,n,s,i=r[1].length>0&&fn(r);return{c(){t=b("div"),e=b("div"),n=I(),i&&i.c(),v(e,"class","editor-container svelte-1r29xbx"),Nt(e,"height",r[3]+"px"),v(t,"class","monaco-diff-container svelte-1r29xbx"),Ft(t,"monaco-diff-container-with-descriptions",r[1].length>0&&r[0])},m(o,l){h(o,t,l),R(t,e),r[24](e),R(t,n),i&&i.m(t,null),s=!0},p(o,l){(!s||8&l[0])&&Nt(e,"height",o[3]+"px"),o[1].length>0?i?(i.p(o,l),2&l[0]&&f(i,1)):(i=fn(o),i.c(),f(i,1),i.m(t,null)):i&&(j(),g(i,1,1,()=>{i=null}),W()),(!s||3&l[0])&&Ft(t,"monaco-diff-container-with-descriptions",o[1].length>0&&o[0])},i(o){s||(f(i),s=!0)},o(o){g(i),s=!1},d(o){o&&m(t),r[24](null),i&&i.d()}}}function gr(r,t,e){let n,s,i;const o=Hs();let{originalCode:l=""}=t,{modifiedCode:a=""}=t,{path:c}=t,{descriptions:u=[]}=t,{lineOffset:d=0}=t,{extraPrefixLines:p=[]}=t,{extraSuffixLines:C=[]}=t,{theme:x}=t,{areDescriptionsVisible:$=!0}=t,{isNewFile:k=!1}=t,{isDeletedFile:_=!1}=t;const B=Ge.getContext().monaco;let D,F,w,E;Rt(r,B,A=>e(23,n=A));let O,S=[];const T=Je();let Z,Q=Kt(0);Rt(r,Q,A=>e(4,s=A));let nt=k?20*a.split(`
`).length+40:100;const ct=n?n.languages.getLanguages().map(A=>A.id):[];function at(A,y){var q,V;if(y){const P=(q=y.split(".").pop())==null?void 0:q.toLowerCase();if(P){const H=(V=n==null?void 0:n.languages.getLanguages().find(Y=>{var X;return(X=Y.extensions)==null?void 0:X.includes("."+P)}))==null?void 0:V.id;if(H&&ct.includes(H))return H}}return"plaintext"}const lt=Kt({});Rt(r,lt,A=>e(5,i=A));let J=null;function ft(){if(!D)return;S=S.filter(q=>(q.dispose(),!1));const A=D.getOriginalEditor(),y=D.getModifiedEditor();S.push(A.onDidScrollChange(()=>{we(Q,s=A.getScrollTop(),s)}),y.onDidScrollChange(()=>{we(Q,s=y.getScrollTop(),s)}))}function mt(){if(!D||!O)return;const A=D.getOriginalEditor(),y=D.getModifiedEditor();S.push(y.onDidContentSizeChange(()=>T.requestLayout()),A.onDidContentSizeChange(()=>T.requestLayout()),D.onDidUpdateDiff(()=>T.requestLayout()),y.onDidChangeHiddenAreas(()=>T.requestLayout()),A.onDidChangeHiddenAreas(()=>T.requestLayout()),y.onDidLayoutChange(()=>T.requestLayout()),A.onDidLayoutChange(()=>T.requestLayout()),y.onDidFocusEditorWidget(()=>{kt(!0)}),A.onDidFocusEditorWidget(()=>{kt(!0)}),y.onDidBlurEditorWidget(()=>{kt(!1)}),A.onDidBlurEditorWidget(()=>{kt(!1)}),y.onDidChangeModelContent(()=>{Ct=!0,Ot=Date.now();const q=(E==null?void 0:E.getValue())||"";if(q===a)return;const V=q.replace(p.join(""),"").replace(C.join(""),"");o("codeChange",{modifiedCode:V});const P=setTimeout(()=>{Ct=!1},500);S.push({dispose:()=>clearTimeout(P)})})),function(){!O||!D||(J&&clearTimeout(J),J=setTimeout(()=>{if(!O.__hasClickListener){const q=V=>{const P=V.target;P&&(P.closest('[title="Show Unchanged Region"]')||P.closest('[title="Hide Unchanged Region"]'))&&At()};O.addEventListener("click",q),e(2,O.__hasClickListener=!0,O),S.push({dispose:()=>{O.removeEventListener("click",q)}})}D&&S.push(D.onDidUpdateDiff(()=>{At()}))},300))}()}Le(()=>{D==null||D.dispose(),F==null||F.dispose(),w==null||w.dispose(),E==null||E.dispose(),S.forEach(A=>A.dispose()),J&&clearTimeout(J),Z==null||Z()});let it=null;function At(){it&&clearTimeout(it),it=setTimeout(()=>{T.requestLayout(),it=null},100),it&&S.push({dispose:()=>{it&&(clearTimeout(it),it=null)}})}function yt(A,y,q,V=[],P=[]){if(!n)return void console.error("Monaco not loaded. Diff view cannot be updated.");w==null||w.dispose(),E==null||E.dispose(),y=y||"",q=q||"";const H=V.join(""),Y=P.join("");if(y=k?q.split(`
`).map(()=>" ").join(`
`):H+y+Y,q=H+q+Y,w=n.editor.createModel(y,void 0,A!==void 0?n.Uri.parse("file://"+A+`#${crypto.randomUUID()}`):void 0),_&&(q=q.split(`
`).map(()=>" ").join(`
`)),e(22,E=n.editor.createModel(q,void 0,A!==void 0?n.Uri.parse("file://"+A+`#${crypto.randomUUID()}`):void 0)),D){D.setModel({original:w,modified:E});const X=D.getOriginalEditor();X&&X.updateOptions({lineNumbers:"off"}),ft(),J&&clearTimeout(J),J=setTimeout(()=>{mt(),J=null},300)}}Gt(()=>{if(n)if(k){e(21,F=n.editor.create(O,{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:x,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:V=>`${d-p.length+V}`}));const A=at(0,c);e(22,E=n.editor.createModel(a,A,c!==void 0?n.Uri.parse("file://"+c+`#${crypto.randomUUID()}`):void 0)),F.setModel(E),S.push(F.onDidChangeModelContent(()=>{Ct=!0,Ot=Date.now();const V=(E==null?void 0:E.getValue())||"";if(V===a)return;o("codeChange",{modifiedCode:V});const P=setTimeout(()=>{Ct=!1},500);S.push({dispose:()=>clearTimeout(P)})})),S.push(F.onDidFocusEditorWidget(()=>{F==null||F.updateOptions({scrollbar:{handleMouseWheel:!0}})}),F.onDidBlurEditorWidget(()=>{F==null||F.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const y=F.getContentHeight();e(3,nt=Math.max(y,60));const q=setTimeout(()=>{F==null||F.layout()},0);S.push({dispose:()=>clearTimeout(q)})}else e(20,D=n.editor.createDiffEditor(O,{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:x,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:A=>`${d-p.length+A}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),Z&&Z(),Z=T.registerEditor({editor:D,updateHeight:Et,id:`monaco-diff-${crypto.randomUUID().slice(0,8)}`}),yt(c,l,a,p,C),ft(),mt(),J&&clearTimeout(J),J=setTimeout(()=>{T.requestLayout(),J=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let Ct=!1,Ot=0;function pt(A,y=!0){return D?(y?D.getModifiedEditor():D.getOriginalEditor()).getTopForLineNumber(A):18*A}function Et(){if(!D)return;const A=D.getModel(),y=A==null?void 0:A.original,q=A==null?void 0:A.modified;if(!y||!q)return;const V=D.getOriginalEditor(),P=D.getModifiedEditor(),H=D.getLineChanges()||[];let Y;if(H.length===0){const X=V.getContentHeight(),G=P.getContentHeight();Y=Math.max(100,X,G)}else{let X=0,G=0;for(const wt of H)wt.originalEndLineNumber>0&&(X=Math.max(X,wt.originalEndLineNumber)),wt.modifiedEndLineNumber>0&&(G=Math.max(G,wt.modifiedEndLineNumber));X=Math.min(X+3,y.getLineCount()),G=Math.min(G+3,q.getLineCount());const ut=V.getTopForLineNumber(X),ht=P.getTopForLineNumber(G);Y=Math.max(ut,ht)+60}e(3,nt=Math.min(Y,2e4)),D.layout(),Jt()}function kt(A){if(!D)return;const y=D.getOriginalEditor(),q=D.getModifiedEditor();y.updateOptions({scrollbar:{handleMouseWheel:A}}),q.updateOptions({scrollbar:{handleMouseWheel:A}})}function U(A){if(!D)return F?F.getTopForLineNumber(A.range.start+1):0;const y=D.getModel(),q=y==null?void 0:y.original,V=y==null?void 0:y.modified;if(!q||!V)return 0;const P=pt(A.range.start+1,!1),H=pt(A.range.start+1,!0);return P&&!H?P:!P&&H?H:Math.min(P,H)}function Jt(){if(!D&&!F||u.length===0)return;const A={};u.forEach((y,q)=>{A[q]=U(y)}),function(y,q=50){const V=Object.keys(y).sort((P,H)=>y[Number(P)]-y[Number(H)]);for(let P=0;P<V.length-1;P++){const H=Number(V[P]),Y=y[H];y[H+1]-Y<q&&(y[Number(V[P+1])]=Y+q)}}(A),lt.set(A)}const Yt=crypto.randomUUID();return r.$$set=A=>{"originalCode"in A&&e(11,l=A.originalCode),"modifiedCode"in A&&e(12,a=A.modifiedCode),"path"in A&&e(13,c=A.path),"descriptions"in A&&e(1,u=A.descriptions),"lineOffset"in A&&e(14,d=A.lineOffset),"extraPrefixLines"in A&&e(15,p=A.extraPrefixLines),"extraSuffixLines"in A&&e(16,C=A.extraSuffixLines),"theme"in A&&e(17,x=A.theme),"areDescriptionsVisible"in A&&e(0,$=A.areDescriptionsVisible),"isNewFile"in A&&e(18,k=A.isNewFile),"isDeletedFile"in A&&e(19,_=A.isDeletedFile)},r.$$.update=()=>{if(16103424&r.$$.dirty[0]&&(A=a,!(Ct||Date.now()-Ot<1e3||E&&E.getValue()===p.join("")+A+C.join(""))))if(k&&F){if(E)E.setValue(a);else{const y=at(0,c);n&&e(22,E=n.editor.createModel(a,y,c!==void 0?n.Uri.parse("file://"+c+`#${crypto.randomUUID()}`):void 0)),E&&F.setModel(E)}e(3,nt=20*a.split(`
`).length+40),F.layout()}else!k&&D&&(yt(c,l,a,p,C),T.requestLayout());var A;if(3145730&r.$$.dirty[0]&&(D||F)&&u.length>0&&Jt(),2363392&r.$$.dirty[0]&&k&&a&&F){const y=F.getContentHeight();e(3,nt=Math.max(y,60)),F.layout()}},[$,u,O,nt,s,i,B,Q,lt,U,Yt,l,a,c,d,p,C,x,k,_,D,F,E,n,function(A){bt[A?"unshift":"push"](()=>{O=A,e(2,O)})},()=>e(0,$=!$)]}let hr=class extends tt{constructor(r){super(),et(this,r,gr,$r,K,{originalCode:11,modifiedCode:12,path:13,descriptions:1,lineOffset:14,extraPrefixLines:15,extraSuffixLines:16,theme:17,areDescriptionsVisible:0,isNewFile:18,isDeletedFile:19},null,[-1,-1])}};const di=Symbol("focusedPath");function pi(){return te(di)}function Ve(r){return`file-diff-${Zt(r)}`}function mr(r){let t,e,n;function s(o){r[41](o)}let i={path:r[3],originalCode:r[0].originalCode,modifiedCode:r[6],theme:r[15],descriptions:r[4],isNewFile:r[21],isDeletedFile:r[20]};return r[1]!==void 0&&(i.areDescriptionsVisible=r[1]),t=new hr({props:i}),bt.push(()=>ee(t,"areDescriptionsVisible",s)),t.$on("codeChange",r[26]),{c(){z(t.$$.fragment)},m(o,l){L(t,o,l),n=!0},p(o,l){const a={};8&l[0]&&(a.path=o[3]),1&l[0]&&(a.originalCode=o[0].originalCode),64&l[0]&&(a.modifiedCode=o[6]),32768&l[0]&&(a.theme=o[15]),16&l[0]&&(a.descriptions=o[4]),2097152&l[0]&&(a.isNewFile=o[21]),1048576&l[0]&&(a.isDeletedFile=o[20]),!e&&2&l[0]&&(e=!0,a.areDescriptionsVisible=o[1],ne(()=>e=!1)),t.$set(a)},i(o){n||(f(t.$$.fragment,o),n=!0)},o(o){g(t.$$.fragment,o),n=!1},d(o){M(t,o)}}}function Dr(r){let t,e,n;return e=new $t({props:{size:1,$$slots:{default:[Cr]},$$scope:{ctx:r}}}),{c(){t=b("div"),z(e.$$.fragment),v(t,"class","too-large-message svelte-1536g7w")},m(s,i){h(s,t,i),L(e,t,null),n=!0},p(s,i){const o={};5888&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function Fr(r){let t,e,n;return e=new $t({props:{$$slots:{default:[kr]},$$scope:{ctx:r}}}),{c(){t=b("div"),z(e.$$.fragment),v(t,"class","binary-file-message svelte-1536g7w")},m(s,i){h(s,t,i),L(e,t,null),n=!0},p(s,i){const o={};2101632&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function xr(r){let t,e,n,s;const i=[br,Ar],o=[];function l(a,c){return a[8]?0:a[6]?1:-1}return~(e=l(r))&&(n=o[e]=i[e](r)),{c(){t=b("div"),n&&n.c(),v(t,"class","image-container svelte-1536g7w")},m(a,c){h(a,t,c),~e&&o[e].m(t,null),s=!0},p(a,c){let u=e;e=l(a),e===u?~e&&o[e].p(a,c):(n&&(j(),g(o[u],1,1,()=>{o[u]=null}),W()),~e?(n=o[e],n?n.p(a,c):(n=o[e]=i[e](a),n.c()),f(n,1),n.m(t,null)):n=null)},i(a){s||(f(n),s=!0)},o(a){g(n),s=!1},d(a){a&&m(t),~e&&o[e].d()}}}function Cr(r){let t,e,n,s,i,o,l,a=ot(r[12])+"",c=(r[8]?r[10]:r[9])+"";return{c(){t=N('File "'),e=N(a),n=N('" is too large to display a diff (size: '),s=N(c),i=N(" bytes, max: "),o=N(oi),l=N(" bytes).")},m(u,d){h(u,t,d),h(u,e,d),h(u,n,d),h(u,s,d),h(u,i,d),h(u,o,d),h(u,l,d)},p(u,d){4096&d[0]&&a!==(a=ot(u[12])+"")&&rt(e,a),1792&d[0]&&c!==(c=(u[8]?u[10]:u[9])+"")&&rt(s,c)},d(u){u&&(m(t),m(e),m(n),m(s),m(i),m(o),m(l))}}}function wr(r){let t,e,n,s=ot(r[12])+"";return{c(){t=N("Binary file modified: "),e=N(s),n=N(".")},m(i,o){h(i,t,o),h(i,e,o),h(i,n,o)},p(i,o){4096&o[0]&&s!==(s=ot(i[12])+"")&&rt(e,s)},d(i){i&&(m(t),m(e),m(n))}}}function vr(r){let t,e,n,s=ot(r[12])+"";return{c(){t=N("Binary file deleted: "),e=N(s),n=N(".")},m(i,o){h(i,t,o),h(i,e,o),h(i,n,o)},p(i,o){4096&o[0]&&s!==(s=ot(i[12])+"")&&rt(e,s)},d(i){i&&(m(t),m(e),m(n))}}}function yr(r){let t,e,n,s=ot(r[12])+"";return{c(){t=N("Binary file added: "),e=N(s),n=N(".")},m(i,o){h(i,t,o),h(i,e,o),h(i,n,o)},p(i,o){4096&o[0]&&s!==(s=ot(i[12])+"")&&rt(e,s)},d(i){i&&(m(t),m(e),m(n))}}}function kr(r){let t;function e(i,o){return i[21]||i[7]?yr:i[8]?vr:wr}let n=e(r),s=n(r);return{c(){s.c(),t=N(`
            No text preview available.`)},m(i,o){s.m(i,o),h(i,t,o)},p(i,o){n===(n=e(i))&&s?s.p(i,o):(s.d(1),s=n(i),s&&(s.c(),s.m(t.parentNode,t)))},d(i){i&&m(t),s.d(i)}}}function Ar(r){let t,e,n,s,i,o,l,a;t=new $t({props:{class:"image-info-text",$$slots:{default:[Br]},$$scope:{ctx:r}}});let c=r[0].originalCode&&r[6]!==r[0].originalCode&&!r[21]&&gn(r);return{c(){z(t.$$.fragment),e=I(),n=b("img"),o=I(),c&&c.c(),l=vt(),qt(n.src,s="data:"+r[19]+";base64,"+btoa(r[6]))||v(n,"src",s),v(n,"alt",i="Current "+ot(r[12])),v(n,"class","image-preview svelte-1536g7w")},m(u,d){L(t,u,d),h(u,e,d),h(u,n,d),h(u,o,d),c&&c.m(u,d),h(u,l,d),a=!0},p(u,d){const p={};2101376&d[0]|16384&d[1]&&(p.$$scope={dirty:d,ctx:u}),t.$set(p),(!a||524352&d[0]&&!qt(n.src,s="data:"+u[19]+";base64,"+btoa(u[6])))&&v(n,"src",s),(!a||4096&d[0]&&i!==(i="Current "+ot(u[12])))&&v(n,"alt",i),u[0].originalCode&&u[6]!==u[0].originalCode&&!u[21]?c?(c.p(u,d),2097217&d[0]&&f(c,1)):(c=gn(u),c.c(),f(c,1),c.m(l.parentNode,l)):c&&(j(),g(c,1,1,()=>{c=null}),W())},i(u){a||(f(t.$$.fragment,u),f(c),a=!0)},o(u){g(t.$$.fragment,u),g(c),a=!1},d(u){u&&(m(e),m(n),m(o),m(l)),M(t,u),c&&c.d(u)}}}function br(r){let t,e,n,s;t=new $t({props:{class:"image-info-text",$$slots:{default:[Lr]},$$scope:{ctx:r}}});let i=r[0].originalCode&&hn(r);return{c(){z(t.$$.fragment),e=I(),i&&i.c(),n=vt()},m(o,l){L(t,o,l),h(o,e,l),i&&i.m(o,l),h(o,n,l),s=!0},p(o,l){const a={};4096&l[0]|16384&l[1]&&(a.$$scope={dirty:l,ctx:o}),t.$set(a),o[0].originalCode?i?(i.p(o,l),1&l[0]&&f(i,1)):(i=hn(o),i.c(),f(i,1),i.m(n.parentNode,n)):i&&(j(),g(i,1,1,()=>{i=null}),W())},i(o){s||(f(t.$$.fragment,o),f(i),s=!0)},o(o){g(t.$$.fragment,o),g(i),s=!1},d(o){o&&(m(e),m(n)),M(t,o),i&&i.d(o)}}}function Er(r){let t;return{c(){t=N("Image modified")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function _r(r){let t;return{c(){t=N("New image added")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function Br(r){let t,e,n=ot(r[12])+"";function s(l,a){return l[21]||l[7]?_r:Er}let i=s(r),o=i(r);return{c(){o.c(),t=N(": "),e=N(n)},m(l,a){o.m(l,a),h(l,t,a),h(l,e,a)},p(l,a){i!==(i=s(l))&&(o.d(1),o=i(l),o&&(o.c(),o.m(t.parentNode,t))),4096&a[0]&&n!==(n=ot(l[12])+"")&&rt(e,n)},d(l){l&&(m(t),m(e)),o.d(l)}}}function gn(r){let t,e,n,s,i,o;return t=new $t({props:{class:"image-info-text",$$slots:{default:[zr]},$$scope:{ctx:r}}}),{c(){z(t.$$.fragment),e=I(),n=b("img"),qt(n.src,s="data:"+jt(r[3])+";base64,"+btoa(r[0].originalCode))||v(n,"src",s),v(n,"alt",i="Original "+ot(r[12])),v(n,"class","image-preview image-preview--previous svelte-1536g7w")},m(l,a){L(t,l,a),h(l,e,a),h(l,n,a),o=!0},p(l,a){const c={};16384&a[1]&&(c.$$scope={dirty:a,ctx:l}),t.$set(c),(!o||9&a[0]&&!qt(n.src,s="data:"+jt(l[3])+";base64,"+btoa(l[0].originalCode)))&&v(n,"src",s),(!o||4096&a[0]&&i!==(i="Original "+ot(l[12])))&&v(n,"alt",i)},i(l){o||(f(t.$$.fragment,l),o=!0)},o(l){g(t.$$.fragment,l),o=!1},d(l){l&&(m(e),m(n)),M(t,l)}}}function zr(r){let t;return{c(){t=N("Previous version:")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function Lr(r){let t,e,n=ot(r[12])+"";return{c(){t=N("Image deleted: "),e=N(n)},m(s,i){h(s,t,i),h(s,e,i)},p(s,i){4096&i[0]&&n!==(n=ot(s[12])+"")&&rt(e,n)},d(s){s&&(m(t),m(e))}}}function hn(r){let t,e,n,s,i,o;return t=new $t({props:{class:"image-info-text",$$slots:{default:[Mr]},$$scope:{ctx:r}}}),{c(){z(t.$$.fragment),e=I(),n=b("img"),qt(n.src,s="data:"+jt(r[3])+";base64,"+btoa(r[0].originalCode))||v(n,"src",s),v(n,"alt",i="Original "+ot(r[12])),v(n,"class","image-preview svelte-1536g7w")},m(l,a){L(t,l,a),h(l,e,a),h(l,n,a),o=!0},p(l,a){const c={};16384&a[1]&&(c.$$scope={dirty:a,ctx:l}),t.$set(c),(!o||9&a[0]&&!qt(n.src,s="data:"+jt(l[3])+";base64,"+btoa(l[0].originalCode)))&&v(n,"src",s),(!o||4096&a[0]&&i!==(i="Original "+ot(l[12])))&&v(n,"alt",i)},i(l){o||(f(t.$$.fragment,l),o=!0)},o(l){g(t.$$.fragment,l),o=!1},d(l){l&&(m(e),m(n)),M(t,l)}}}function Mr(r){let t;return{c(){t=N("Previous version:")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function Rr(r){let t,e,n,s;const i=[xr,Fr,Dr,mr],o=[];function l(a,c){return a[18]?0:a[17]?1:a[16]?2:3}return e=l(r),n=o[e]=i[e](r),{c(){t=b("div"),n.c(),v(t,"class","changes svelte-1536g7w")},m(a,c){h(a,t,c),o[e].m(t,null),s=!0},p(a,c){let u=e;e=l(a),e===u?o[e].p(a,c):(j(),g(o[u],1,1,()=>{o[u]=null}),W(),n=o[e],n?n.p(a,c):(n=o[e]=i[e](a),n.c()),f(n,1),n.m(t,null))},i(a){s||(f(n),s=!0)},o(a){g(n),s=!1},d(a){a&&m(t),o[e].d()}}}function Or(r){let t,e=ot(r[12])+"";return{c(){t=N(e)},m(n,s){h(n,t,s)},p(n,s){4096&s[0]&&e!==(e=ot(n[12])+"")&&rt(t,e)},d(n){n&&m(t)}}}function Nr(r){let t,e;return t=new Tt({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$slots:{default:[Or]},$$scope:{ctx:r}}}),t.$on("click",r[28]),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};4096&s[0]|16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function mn(r){let t,e,n=se(r[12])+"";return{c(){t=b("span"),e=N(n),v(t,"class","c-directory svelte-1536g7w")},m(s,i){h(s,t,i),R(t,e)},p(s,i){4096&i[0]&&n!==(n=se(s[12])+"")&&rt(e,n)},d(s){s&&m(t)}}}function qr(r){let t,e,n,s=r[23]>0&&Dn(r),i=r[22]>0&&Fn(r);return{c(){t=b("div"),s&&s.c(),e=I(),i&&i.c(),v(t,"class","changes-indicator svelte-1536g7w")},m(o,l){h(o,t,l),s&&s.m(t,null),R(t,e),i&&i.m(t,null),n=!0},p(o,l){o[23]>0?s?(s.p(o,l),8388608&l[0]&&f(s,1)):(s=Dn(o),s.c(),f(s,1),s.m(t,e)):s&&(j(),g(s,1,1,()=>{s=null}),W()),o[22]>0?i?(i.p(o,l),4194304&l[0]&&f(i,1)):(i=Fn(o),i.c(),f(i,1),i.m(t,null)):i&&(j(),g(i,1,1,()=>{i=null}),W())},i(o){n||(f(s),f(i),n=!0)},o(o){g(s),g(i),n=!1},d(o){o&&m(t),s&&s.d(),i&&i.d()}}}function Sr(r){let t;return{c(){t=b("span"),t.textContent="New File",v(t,"class","new-file-badge svelte-1536g7w")},m(e,n){h(e,t,n)},p:st,i:st,o:st,d(e){e&&m(t)}}}function Dn(r){let t,e,n;return e=new $t({props:{size:1,$$slots:{default:[Tr]},$$scope:{ctx:r}}}),{c(){t=b("span"),z(e.$$.fragment),v(t,"class","additions svelte-1536g7w")},m(s,i){h(s,t,i),L(e,t,null),n=!0},p(s,i){const o={};8388608&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function Tr(r){let t,e;return{c(){t=N("+"),e=N(r[23])},m(n,s){h(n,t,s),h(n,e,s)},p(n,s){8388608&s[0]&&rt(e,n[23])},d(n){n&&(m(t),m(e))}}}function Fn(r){let t,e,n;return e=new $t({props:{size:1,$$slots:{default:[Pr]},$$scope:{ctx:r}}}),{c(){t=b("span"),z(e.$$.fragment),v(t,"class","deletions svelte-1536g7w")},m(s,i){h(s,t,i),L(e,t,null),n=!0},p(s,i){const o={};4194304&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function Pr(r){let t,e;return{c(){t=N("-"),e=N(r[22])},m(n,s){h(n,t,s),h(n,e,s)},p(n,s){4194304&s[0]&&rt(e,n[22])},d(n){n&&(m(t),m(e))}}}function Ir(r){let t;return{c(){t=N("Apply")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function Ur(r){let t;return{c(){t=N("Applied")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function Vr(r){let t,e,n;return e=new fe({}),{c(){t=b("div"),z(e.$$.fragment),v(t,"class","applied__icon svelte-1536g7w")},m(s,i){h(s,t,i),L(e,t,null),n=!0},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function Zr(r){let t,e,n;return e=new Re({props:{iconName:"check"}}),{c(){t=b("div"),z(e.$$.fragment),v(t,"class","applied svelte-1536g7w")},m(s,i){h(s,t,i),L(e,t,null),n=!0},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function Hr(r){let t,e,n,s,i;function o(p,C){return p[5]?Ur:Ir}let l=o(r),a=l(r);const c=[Zr,Vr],u=[];function d(p,C){return p[5]?0:1}return e=d(r),n=u[e]=c[e](r),{c(){a.c(),t=I(),n.c(),s=vt()},m(p,C){a.m(p,C),h(p,t,C),u[e].m(p,C),h(p,s,C),i=!0},p(p,C){l!==(l=o(p))&&(a.d(1),a=l(p),a&&(a.c(),a.m(t.parentNode,t)));let x=e;e=d(p),e!==x&&(j(),g(u[x],1,1,()=>{u[x]=null}),W(),n=u[e],n||(n=u[e]=c[e](p),n.c()),f(n,1),n.m(s.parentNode,s))},i(p){i||(f(n),i=!0)},o(p){g(n),i=!1},d(p){p&&(m(t),m(s)),a.d(p),u[e].d(p)}}}function jr(r){let t,e;return t=new Tt({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[14],$$slots:{default:[Hr]},$$scope:{ctx:r}}}),t.$on("click",r[27]),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};16384&s[0]&&(i.disabled=n[14]),32&s[0]|16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function xn(r){let t,e;return t=new Vt({props:{content:r[11],triggerOn:[re.Hover],delayDurationMs:300,$$slots:{default:[Qr]},$$scope:{ctx:r}}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};2048&s[0]&&(i.content=n[11]),16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function Wr(r){let t,e;return t=new Qe({}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function Qr(r){let t,e;return t=new pe({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[Wr]},$$scope:{ctx:r}}}),t.$on("click",r[28]),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function Gr(r){let t,e,n,s,i,o,l,a,c,u,d,p,C,x=se(r[12]);e=new ti({}),i=new Vt({props:{content:r[11],triggerOn:[re.Hover],delayDurationMs:300,$$slots:{default:[Nr]},$$scope:{ctx:r}}});let $=x&&mn(r);const k=[Sr,qr],_=[];function B(F,w){return F[21]?0:1}a=B(r),c=_[a]=k[a](r),d=new Vt({props:{content:r[13],triggerOn:[re.Hover],delayDurationMs:300,$$slots:{default:[jr]},$$scope:{ctx:r}}});let D=r[5]&&xn(r);return{c(){t=b("div"),z(e.$$.fragment),n=I(),s=b("div"),z(i.$$.fragment),o=I(),$&&$.c(),l=I(),c.c(),u=I(),z(d.$$.fragment),p=I(),D&&D.c(),v(s,"class","c-path svelte-1536g7w"),v(t,"slot","header"),v(t,"class","header svelte-1536g7w")},m(F,w){h(F,t,w),L(e,t,null),R(t,n),R(t,s),L(i,s,null),R(s,o),$&&$.m(s,null),R(t,l),_[a].m(t,null),R(t,u),L(d,t,null),R(t,p),D&&D.m(t,null),C=!0},p(F,w){const E={};2048&w[0]&&(E.content=F[11]),4096&w[0]|16384&w[1]&&(E.$$scope={dirty:w,ctx:F}),i.$set(E),4096&w[0]&&(x=se(F[12])),x?$?$.p(F,w):($=mn(F),$.c(),$.m(s,null)):$&&($.d(1),$=null);let O=a;a=B(F),a===O?_[a].p(F,w):(j(),g(_[O],1,1,()=>{_[O]=null}),W(),c=_[a],c?c.p(F,w):(c=_[a]=k[a](F),c.c()),f(c,1),c.m(t,u));const S={};8192&w[0]&&(S.content=F[13]),16416&w[0]|16384&w[1]&&(S.$$scope={dirty:w,ctx:F}),d.$set(S),F[5]?D?(D.p(F,w),32&w[0]&&f(D,1)):(D=xn(F),D.c(),f(D,1),D.m(t,null)):D&&(j(),g(D,1,1,()=>{D=null}),W())},i(F){C||(f(e.$$.fragment,F),f(i.$$.fragment,F),f(c),f(d.$$.fragment,F),f(D),C=!0)},o(F){g(e.$$.fragment,F),g(i.$$.fragment,F),g(c),g(d.$$.fragment,F),g(D),C=!1},d(F){F&&m(t),M(e),M(i),$&&$.d(),_[a].d(),M(d),D&&D.d()}}}function Jr(r){let t,e,n,s,i;function o(a){r[42](a)}let l={stickyHeader:!0,$$slots:{header:[Gr],default:[Rr]},$$scope:{ctx:r}};return r[2]!==void 0&&(l.collapsed=r[2]),e=new Ks({props:l}),bt.push(()=>ee(e,"collapsed",o)),{c(){t=b("div"),z(e.$$.fragment),v(t,"class","c svelte-1536g7w"),v(t,"id",s=Ve(r[3])),Ft(t,"focused",r[24]===r[3])},m(a,c){h(a,t,c),L(e,t,null),i=!0},p(a,c){const u={};16777211&c[0]|16384&c[1]&&(u.$$scope={dirty:c,ctx:a}),!n&&4&c[0]&&(n=!0,u.collapsed=a[2],ne(()=>n=!1)),e.$set(u),(!i||8&c[0]&&s!==(s=Ve(a[3])))&&v(t,"id",s),(!i||16777224&c[0])&&Ft(t,"focused",a[24]===a[3])},i(a){i||(f(e.$$.fragment,a),i=!0)},o(a){g(e.$$.fragment,a),i=!1},d(a){a&&m(t),M(e)}}}function Yr(r,t,e){let n,s,i,o,l,a,c,u,d,p,C,x,$,k,_,B,D,F,w,E,O,S,T;Rt(r,js,U=>e(40,S=U));let{path:Z}=t,{change:Q}=t,{descriptions:nt=[]}=t,{areDescriptionsVisible:ct=!0}=t,{isExpandedDefault:at}=t,{isCollapsed:lt=!at}=t,{isApplying:J}=t,{hasApplied:ft}=t,{onApplyChanges:mt}=t,{onCodeChange:it}=t,{onOpenFile:At}=t,{isAgentFromDifferentRepo:yt=!1}=t;const Ct=pi();Rt(r,Ct,U=>e(24,T=U));const Ot=te(oe.key);let pt=Q.modifiedCode,Et=w;function kt(){e(11,Et=`Open ${w??"file"}`)}return Gt(()=>{kt()}),r.$$set=U=>{"path"in U&&e(3,Z=U.path),"change"in U&&e(0,Q=U.change),"descriptions"in U&&e(4,nt=U.descriptions),"areDescriptionsVisible"in U&&e(1,ct=U.areDescriptionsVisible),"isExpandedDefault"in U&&e(29,at=U.isExpandedDefault),"isCollapsed"in U&&e(2,lt=U.isCollapsed),"isApplying"in U&&e(30,J=U.isApplying),"hasApplied"in U&&e(5,ft=U.hasApplied),"onApplyChanges"in U&&e(31,mt=U.onApplyChanges),"onCodeChange"in U&&e(32,it=U.onCodeChange),"onOpenFile"in U&&e(33,At=U.onOpenFile),"isAgentFromDifferentRepo"in U&&e(34,yt=U.isAgentFromDifferentRepo)},r.$$.update=()=>{var U;1&r.$$.dirty[0]&&e(6,pt=Q.modifiedCode),1&r.$$.dirty[0]&&e(39,n=Ue(Q.diff)),256&r.$$.dirty[1]&&e(23,s=n.additions),256&r.$$.dirty[1]&&e(22,i=n.deletions),1&r.$$.dirty[0]&&e(21,o=ci(Q)),1&r.$$.dirty[0]&&e(20,l=ui(Q)),8&r.$$.dirty[0]&&e(38,a=si(Z)),8&r.$$.dirty[0]&&e(19,c=jt(Z)),8&r.$$.dirty[0]&&e(37,u=ii(Z)),1&r.$$.dirty[0]&&e(10,d=((U=Q.originalCode)==null?void 0:U.length)||0),64&r.$$.dirty[0]&&e(9,p=(pt==null?void 0:pt.length)||0),1024&r.$$.dirty[0]&&e(36,C=ke(d)),512&r.$$.dirty[0]&&e(35,x=ke(p)),65&r.$$.dirty[0]&&e(8,$=!pt&&!!Q.originalCode),65&r.$$.dirty[0]&&e(7,k=!!pt&&!Q.originalCode),128&r.$$.dirty[1]&&e(18,_=a),192&r.$$.dirty[1]&&e(17,B=!a&&u),384&r.$$.dirty[0]|240&r.$$.dirty[1]&&e(16,D=!a&&!u&&(x||$&&C||k&&x)),512&r.$$.dirty[1]&&e(15,F=ni(S==null?void 0:S.category,S==null?void 0:S.intensity)),8&r.$$.dirty[0]&&e(12,w=ri(Z)),1073741824&r.$$.dirty[0]|8&r.$$.dirty[1]&&e(14,E=J||yt),1073741856&r.$$.dirty[0]|8&r.$$.dirty[1]&&e(13,O=J?"Applying changes...":ft?"Reapply changes to local file":yt?"Cannot apply changes from a different repository locally":"Apply changes to local file")},[Q,ct,lt,Z,nt,ft,pt,k,$,p,d,Et,w,O,E,F,D,B,_,c,l,o,i,s,T,Ct,function(U){e(6,pt=U.detail.modifiedCode),it==null||it(pt)},function(){Ot.reportApplyChangesEvent(),e(0,Q.modifiedCode=pt,Q),it==null||it(pt),mt==null||mt()},async function(){At&&(e(11,Et="Opening file..."),await At()?kt():(e(11,Et="Failed to open file. Does the file exist?"),setTimeout(()=>{kt()},2e3)))},at,J,mt,it,At,yt,x,C,u,a,n,S,function(U){ct=U,e(1,ct)},function(U){lt=U,e(2,lt)}]}let Xr=class extends tt{constructor(r){super(),et(this,r,Yr,Jr,K,{path:3,change:0,descriptions:4,areDescriptionsVisible:1,isExpandedDefault:29,isCollapsed:2,isApplying:30,hasApplied:5,onApplyChanges:31,onCodeChange:32,onOpenFile:33,isAgentFromDifferentRepo:34},null,[-1,-1])}};function Cn(r,t,e){const n=r.slice();return n[6]=t[e],n}function Kr(r){let t,e;return t=new li({props:{filename:r[0].name}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};1&s&&(i.filename=n[0].name),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function to(r){let t,e;return t=new $e({props:{icon:r[0].isExpanded?"chevron-down":"chevron-right"}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};1&s&&(i.icon=n[0].isExpanded?"chevron-down":"chevron-right"),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function eo(r){let t,e,n=(r[0].displayName||r[0].name)+"";return{c(){t=b("span"),e=N(n),v(t,"class","full-path-text svelte-qnxoj")},m(s,i){h(s,t,i),R(t,e)},p(s,i){1&i&&n!==(n=(s[0].displayName||s[0].name)+"")&&rt(e,n)},d(s){s&&m(t)}}}function wn(r){let t,e,n=dt(Array.from(r[0].children.values()).sort(yn)),s=[];for(let o=0;o<n.length;o+=1)s[o]=vn(Cn(r,n,o));const i=o=>g(s[o],1,1,()=>{s[o]=null});return{c(){t=b("div");for(let o=0;o<s.length;o+=1)s[o].c();v(t,"class","tree-node__children svelte-qnxoj"),v(t,"role","group")},m(o,l){h(o,t,l);for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(t,null);e=!0},p(o,l){if(3&l){let a;for(n=dt(Array.from(o[0].children.values()).sort(yn)),a=0;a<n.length;a+=1){const c=Cn(o,n,a);s[a]?(s[a].p(c,l),f(s[a],1)):(s[a]=vn(c),s[a].c(),f(s[a],1),s[a].m(t,null))}for(j(),a=n.length;a<s.length;a+=1)i(a);W()}},i(o){if(!e){for(let l=0;l<n.length;l+=1)f(s[l]);e=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)g(s[l]);e=!1},d(o){o&&m(t),Mt(s,o)}}}function vn(r){let t,e;return t=new fi({props:{node:r[6],indentLevel:r[1]+1}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};1&s&&(i.node=n[6]),2&s&&(i.indentLevel=n[1]+1),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function no(r){let t,e,n,s,i,o,l,a,c,u,d,p,C,x,$,k,_;const B=[to,Kr],D=[];function F(E,O){return E[0].isFile?1:0}o=F(r),l=D[o]=B[o](r),u=new $t({props:{size:1,$$slots:{default:[eo]},$$scope:{ctx:r}}});let w=!r[0].isFile&&r[0].isExpanded&&r[0].children.size>0&&wn(r);return{c(){t=b("div"),e=b("div"),n=b("div"),s=I(),i=b("div"),l.c(),a=I(),c=b("span"),z(u.$$.fragment),x=I(),w&&w.c(),v(n,"class","tree-node__indent svelte-qnxoj"),Nt(n,"width",6*r[1]+"px"),v(i,"class","tree-node__icon-container svelte-qnxoj"),v(c,"class","tree-node__label svelte-qnxoj"),v(c,"title",d=r[0].displayName||r[0].name),Ft(c,"full-path",r[0].displayName),v(e,"class","tree-node__content svelte-qnxoj"),v(e,"role","treeitem"),v(e,"tabindex","0"),v(e,"aria-selected",p=r[0].path===r[2]),v(e,"aria-expanded",C=r[0].isFile?void 0:r[0].isExpanded),Ft(e,"selected",r[0].path===r[2]),Ft(e,"collapsed-folder",r[0].displayName&&!r[0].isFile),v(t,"class","tree-node svelte-qnxoj")},m(E,O){h(E,t,O),R(t,e),R(e,n),R(e,s),R(e,i),D[o].m(i,null),R(e,a),R(e,c),L(u,c,null),R(t,x),w&&w.m(t,null),$=!0,k||(_=[Qt(e,"click",r[4]),Qt(e,"keydown",r[5])],k=!0)},p(E,[O]){(!$||2&O)&&Nt(n,"width",6*E[1]+"px");let S=o;o=F(E),o===S?D[o].p(E,O):(j(),g(D[S],1,1,()=>{D[S]=null}),W(),l=D[o],l?l.p(E,O):(l=D[o]=B[o](E),l.c()),f(l,1),l.m(i,null));const T={};513&O&&(T.$$scope={dirty:O,ctx:E}),u.$set(T),(!$||1&O&&d!==(d=E[0].displayName||E[0].name))&&v(c,"title",d),(!$||1&O)&&Ft(c,"full-path",E[0].displayName),(!$||5&O&&p!==(p=E[0].path===E[2]))&&v(e,"aria-selected",p),(!$||1&O&&C!==(C=E[0].isFile?void 0:E[0].isExpanded))&&v(e,"aria-expanded",C),(!$||5&O)&&Ft(e,"selected",E[0].path===E[2]),(!$||1&O)&&Ft(e,"collapsed-folder",E[0].displayName&&!E[0].isFile),!E[0].isFile&&E[0].isExpanded&&E[0].children.size>0?w?(w.p(E,O),1&O&&f(w,1)):(w=wn(E),w.c(),f(w,1),w.m(t,null)):w&&(j(),g(w,1,1,()=>{w=null}),W())},i(E){$||(f(l),f(u.$$.fragment,E),f(w),$=!0)},o(E){g(l),g(u.$$.fragment,E),g(w),$=!1},d(E){E&&m(t),D[o].d(),M(u),w&&w.d(),k=!1,ze(_)}}}const yn=(r,t)=>r.isFile===t.isFile?r.name.localeCompare(t.name):r.isFile?1:-1;function so(r,t,e){let n,{node:s}=t,{indentLevel:i=0}=t;const o=pi();function l(){s.isFile?o.set(s.path):e(0,s.isExpanded=!s.isExpanded,s)}return Rt(r,o,a=>e(2,n=a)),r.$$set=a=>{"node"in a&&e(0,s=a.node),"indentLevel"in a&&e(1,i=a.indentLevel)},[s,i,n,o,l,a=>a.key==="Enter"&&l()]}class fi extends tt{constructor(t){super(),et(this,t,so,no,K,{node:0,indentLevel:1})}}function kn(r,t,e){const n=r.slice();return n[4]=t[e],n}function io(r){let t,e,n=dt(Array.from(r[1].children.values()).sort(bn)),s=[];for(let o=0;o<n.length;o+=1)s[o]=An(kn(r,n,o));const i=o=>g(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();t=vt()},m(o,l){for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(o,l);h(o,t,l),e=!0},p(o,l){if(2&l){let a;for(n=dt(Array.from(o[1].children.values()).sort(bn)),a=0;a<n.length;a+=1){const c=kn(o,n,a);s[a]?(s[a].p(c,l),f(s[a],1)):(s[a]=An(c),s[a].c(),f(s[a],1),s[a].m(t.parentNode,t))}for(j(),a=n.length;a<s.length;a+=1)i(a);W()}},i(o){if(!e){for(let l=0;l<n.length;l+=1)f(s[l]);e=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)g(s[l]);e=!1},d(o){o&&m(t),Mt(s,o)}}}function ro(r){let t,e,n;return e=new $t({props:{size:1,color:"neutral",$$slots:{default:[lo]},$$scope:{ctx:r}}}),{c(){t=b("div"),z(e.$$.fragment),v(t,"class","tree-view__empty svelte-1tnd9l7")},m(s,i){h(s,t,i),L(e,t,null),n=!0},p(s,i){const o={};128&i&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function oo(r){let t;return{c(){t=b("div"),t.innerHTML='<div class="tree-view__skeleton svelte-1tnd9l7"><div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="width: 70%;"></div></div>',v(t,"class","tree-view__loading svelte-1tnd9l7")},m(e,n){h(e,t,n)},p:st,i:st,o:st,d(e){e&&m(t)}}}function An(r){let t,e;return t=new fi({props:{node:r[4],indentLevel:0}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};2&s&&(i.node=n[4]),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function lo(r){let t;return{c(){t=N("No changed files")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function ao(r){let t,e,n,s,i;const o=[oo,ro,io],l=[];function a(c,u){return c[0]?0:c[1].children.size===0?1:2}return n=a(r),s=l[n]=o[n](r),{c(){t=b("div"),e=b("div"),s.c(),v(e,"class","tree-view__content svelte-1tnd9l7"),v(e,"role","tree"),v(e,"aria-label","Changed Files"),v(t,"class","tree-view svelte-1tnd9l7")},m(c,u){h(c,t,u),R(t,e),l[n].m(e,null),i=!0},p(c,[u]){let d=n;n=a(c),n===d?l[n].p(c,u):(j(),g(l[d],1,1,()=>{l[d]=null}),W(),s=l[n],s?s.p(c,u):(s=l[n]=o[n](c),s.c()),f(s,1),s.m(e,null))},i(c){i||(f(s),i=!0)},o(c){g(s),i=!1},d(c){c&&m(t),l[n].d()}}}function Ze(r,t=!1){if(r.isFile)return;let e="";t&&(e=function(o){let l=o.path.split("/"),a=o;for(;;){const c=Array.from(a.children.values()).filter(d=>!d.isFile),u=Array.from(a.children.values()).filter(d=>d.isFile);if(c.length!==1||u.length!==0)break;a=c[0],l.push(a.name)}return l.join("/")}(r));const n=Array.from(r.children.values()).filter(o=>!o.isFile);for(const o of n)Ze(o);const s=Array.from(r.children.values()).filter(o=>!o.isFile),i=Array.from(r.children.values()).filter(o=>o.isFile);if(s.length===1&&i.length===0){const o=s[0],l=o.name;if(t){r.displayName=e||`${r.name}/${l}`;for(const[a,c]of o.children.entries()){const u=`${a}`;r.children.set(u,c)}r.children.delete(l)}else{r.displayName?o.displayName=`${r.displayName}/${l}`:o.displayName=`${r.name}/${l}`;for(const[a,c]of o.children.entries()){const u=`${l}/${a}`;r.children.set(u,c)}r.children.delete(l)}}}const bn=(r,t)=>r.isFile===t.isFile?r.name.localeCompare(t.name):r.isFile?1:-1;function co(r,t,e){let n,{changedFiles:s=[]}=t,{isLoading:i=!1}=t;function o(l){const a={name:"",path:"",isFile:!1,children:new Map,isExpanded:!0};return l.forEach(c=>{const u=c.change_type===Wi.deleted?c.old_path:c.new_path;u&&function(d,p){const C=p.split("/");let x=d;for(let $=0;$<C.length;$++){const k=C[$],_=$===C.length-1,B=C.slice(0,$+1).join("/");x.children.has(k)||x.children.set(k,{name:k,path:B,isFile:_,children:new Map,isExpanded:!0}),x=x.children.get(k)}}(a,u)}),function(c){if(!c.isFile)if(c.path!=="")Ze(c);else{const u=Array.from(c.children.values()).filter(d=>!d.isFile);for(const d of u)Ze(d,!0)}}(a),a}return r.$$set=l=>{"changedFiles"in l&&e(2,s=l.changedFiles),"isLoading"in l&&e(0,i=l.isLoading)},r.$$.update=()=>{4&r.$$.dirty&&e(1,n=o(s))},[i,n,s]}class $i extends tt{constructor(t){super(),et(this,t,co,ao,K,{changedFiles:2,isLoading:0})}}function En(r,t,e){const n=r.slice();return n[19]=t[e],n}function uo(r){let t;return{c(){t=N("Changed files")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function po(r){let t,e,n;return e=new $t({props:{size:1,color:"neutral",$$slots:{default:[$o]},$$scope:{ctx:r}}}),{c(){t=b("div"),z(e.$$.fragment),v(t,"class","c-edits-list c-edits-list--empty svelte-6iqvaj")},m(s,i){h(s,t,i),L(e,t,null),n=!0},p(s,i){const o={};4194304&i&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function fo(r){let t,e,n,s,i,o,l=[],a=new Map,c=r[9].length>0&&_n(r),u=dt(r[9]);const d=p=>p[19].qualifiedPathName.relPath;for(let p=0;p<u.length;p+=1){let C=En(r,u,p),x=d(C);a.set(x,l[p]=Bn(x,C))}return{c(){t=b("div"),e=b("div"),c&&c.c(),n=I(),s=b("div"),i=b("div");for(let p=0;p<l.length;p+=1)l[p].c();v(e,"class","c-edits-list-controls svelte-6iqvaj"),v(t,"class","c-edits-list-header svelte-6iqvaj"),v(i,"class","c-edits-section svelte-6iqvaj"),v(s,"class","c-edits-list svelte-6iqvaj")},m(p,C){h(p,t,C),R(t,e),c&&c.m(e,null),h(p,n,C),h(p,s,C),R(s,i);for(let x=0;x<l.length;x+=1)l[x]&&l[x].m(i,null);o=!0},p(p,C){p[9].length>0?c?(c.p(p,C),512&C&&f(c,1)):(c=_n(p),c.c(),f(c,1),c.m(e,null)):c&&(j(),g(c,1,1,()=>{c=null}),W()),2654&C&&(u=dt(p[9]),j(),l=Js(l,C,d,1,p,u,a,i,Ys,Bn,null,En),W())},i(p){if(!o){f(c);for(let C=0;C<u.length;C+=1)f(l[C]);o=!0}},o(p){g(c);for(let C=0;C<l.length;C+=1)g(l[C]);o=!1},d(p){p&&(m(t),m(n),m(s)),c&&c.d();for(let C=0;C<l.length;C+=1)l[C].d()}}}function $o(r){let t;return{c(){t=N("No changes to show")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function _n(r){let t,e;return t=new Tt({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[7]||r[8]||r[3].length>0||!r[10],$$slots:{default:[Do]},$$scope:{ctx:r}}}),t.$on("click",r[12]),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};1416&s&&(i.disabled=n[7]||n[8]||n[3].length>0||!n[10]),4194688&s&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function go(r){let t;return{c(){t=N("Apply all")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function ho(r){let t;return{c(){t=N("All applied")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function mo(r){let t;return{c(){t=N("Applying...")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function Do(r){let t,e,n,s;function i(a,c){return a[7]?mo:a[8]?ho:go}let o=i(r),l=o(r);return n=new fe({}),{c(){l.c(),t=I(),e=b("div"),z(n.$$.fragment),v(e,"class","c-edits-list-controls__icon svelte-6iqvaj")},m(a,c){l.m(a,c),h(a,t,c),h(a,e,c),L(n,e,null),s=!0},p(a,c){o!==(o=i(a))&&(l.d(1),l=o(a),l&&(l.c(),l.m(t.parentNode,t)))},i(a){s||(f(n.$$.fragment,a),s=!0)},o(a){g(n.$$.fragment,a),s=!1},d(a){a&&(m(t),m(e)),l.d(a),M(n)}}}function Bn(r,t){let e,n,s,i,o;function l(...u){return t[16](t[19],...u)}function a(){return t[17](t[19])}function c(){return t[18](t[19])}return n=new Xr({props:{path:t[19].qualifiedPathName.relPath,change:t[19].diff,isApplying:t[3].includes(t[19].qualifiedPathName.relPath),hasApplied:t[4].includes(t[19].qualifiedPathName.relPath),onCodeChange:l,onApplyChanges:a,onOpenFile:t[2]?c:void 0,isExpandedDefault:!0}}),{key:r,first:null,c(){e=b("div"),z(n.$$.fragment),s=I(),v(e,"class",""),this.first=e},m(u,d){h(u,e,d),L(n,e,null),R(e,s),o=!0},p(u,d){t=u;const p={};512&d&&(p.path=t[19].qualifiedPathName.relPath),512&d&&(p.change=t[19].diff),520&d&&(p.isApplying=t[3].includes(t[19].qualifiedPathName.relPath)),528&d&&(p.hasApplied=t[4].includes(t[19].qualifiedPathName.relPath)),512&d&&(p.onCodeChange=l),578&d&&(p.onApplyChanges=a),516&d&&(p.onOpenFile=t[2]?c:void 0),n.$set(p)},i(u){o||(f(n.$$.fragment,u),u&&Ei(()=>{o&&(i||(i=on(e,cn,{},!0)),i.run(1))}),o=!0)},o(u){g(n.$$.fragment,u),u&&(i||(i=on(e,cn,{},!1)),i.run(0)),o=!1},d(u){u&&m(e),M(n),u&&i&&i.end()}}}function Fo(r){let t,e,n,s,i,o,l,a,c,u,d,p;i=new $t({props:{size:1,class:"c-file-explorer__tree__header__label",$$slots:{default:[uo]},$$scope:{ctx:r}}}),l=new $i({props:{changedFiles:r[0],isLoading:r[5]}});const C=[fo,po],x=[];function $(k,_){return k[9].length>0?0:1}return u=$(r),d=x[u]=C[u](r),{c(){t=b("div"),e=b("div"),n=b("div"),s=b("div"),z(i.$$.fragment),o=I(),z(l.$$.fragment),a=I(),c=b("div"),d.c(),v(s,"class","c-file-explorer__tree__header svelte-6iqvaj"),v(n,"class","c-file-explorer__tree svelte-6iqvaj"),v(c,"class","c-file-explorer__details svelte-6iqvaj"),v(e,"class","c-file-explorer__layout svelte-6iqvaj"),v(t,"class","c-edits-list-container svelte-6iqvaj")},m(k,_){h(k,t,_),R(t,e),R(e,n),R(n,s),L(i,s,null),R(s,o),L(l,s,null),R(e,a),R(e,c),x[u].m(c,null),p=!0},p(k,[_]){const B={};4194304&_&&(B.$$scope={dirty:_,ctx:k}),i.$set(B);const D={};1&_&&(D.changedFiles=k[0]),32&_&&(D.isLoading=k[5]),l.$set(D);let F=u;u=$(k),u===F?x[u].p(k,_):(j(),g(x[F],1,1,()=>{x[F]=null}),W(),d=x[u],d?d.p(k,_):(d=x[u]=C[u](k),d.c()),f(d,1),d.m(c,null))},i(k){p||(f(i.$$.fragment,k),f(l.$$.fragment,k),f(d),p=!0)},o(k){g(i.$$.fragment,k),g(l.$$.fragment,k),g(d),p=!1},d(k){k&&m(t),M(i),M(l),x[u].d()}}}function xo(r,t,e){let n,s,i,o,l,{changedFiles:a}=t,{onApplyChanges:c}=t,{onOpenFile:u}=t,{pendingFiles:d=[]}=t,{appliedFiles:p=[]}=t,{isLoadingTreeView:C=!1}=t,x={},$=!1,k=!1;function _(B,D){e(6,x[B]=D,x)}return r.$$set=B=>{"changedFiles"in B&&e(0,a=B.changedFiles),"onApplyChanges"in B&&e(1,c=B.onApplyChanges),"onOpenFile"in B&&e(2,u=B.onOpenFile),"pendingFiles"in B&&e(3,d=B.pendingFiles),"appliedFiles"in B&&e(4,p=B.appliedFiles),"isLoadingTreeView"in B&&e(5,C=B.isLoadingTreeView)},r.$$.update=()=>{if(1&r.$$.dirty&&e(15,n=JSON.stringify(a)),16&r.$$.dirty&&e(13,s=JSON.stringify(p)),8&r.$$.dirty&&e(14,i=JSON.stringify(d)),32768&r.$$.dirty&&n&&(e(6,x={}),e(7,$=!1),e(8,k=!1)),65&r.$$.dirty&&e(9,l=a.map(B=>{const D=B.new_path||B.old_path,F=B.old_contents||"",w=B.new_contents||"",E=Xi.generateDiff(B.old_path,B.new_path,F,w),O=function(S,T){const Z=ye("oldFile","newFile",S,T,"","",{context:3}),Q=Ri(Z);let nt=0,ct=0,at=[];for(const lt of Q)for(const J of lt.hunks)for(const ft of J.lines){const mt=ft.startsWith("+"),it=ft.startsWith("-");mt&&nt++,it&&ct++,at.push({value:ft,added:mt,removed:it})}return{totalAddedLines:nt,totalRemovedLines:ct,changes:at,diff:Z}}(F,w);return x[D]||e(6,x[D]=w,x),{qualifiedPathName:{rootPath:"",relPath:D},lineChanges:O,oldContents:F,newContents:w,diff:E}})),57880&r.$$.dirty&&e(10,o=(()=>{if(n&&s&&i){const B=l.map(D=>D.qualifiedPathName.relPath);return B.length!==0&&B.some(D=>!p.includes(D)&&!d.includes(D))}return!1})()),664&r.$$.dirty&&$){const B=l.map(D=>D.qualifiedPathName.relPath);B.filter(D=>!p.includes(D)&&!d.includes(D)).length===0&&B.every(D=>p.includes(D)||d.includes(D))&&d.length===0&&p.length>0&&(e(7,$=!1),e(8,k=!0))}if(9104&r.$$.dirty&&l.length>0&&!$&&s){const B=l.map(D=>D.qualifiedPathName.relPath);if(B.length>0){const D=B.every(F=>p.includes(F));D&&p.length>0?e(8,k=!0):!D&&k&&e(8,k=!1)}}},[a,c,u,d,p,C,x,$,k,l,o,_,function(){if(!c)return;const B=l.map(F=>F.qualifiedPathName.relPath);if(B.every(F=>p.includes(F)))return void e(8,k=!0);const D=B.filter(F=>!p.includes(F)&&!d.includes(F));D.length!==0&&(e(7,$=!0),e(8,k=!1),D.forEach(F=>{const w=l.find(E=>E.qualifiedPathName.relPath===F);if(w){const E=x[F]||w.newContents;c(F,w.oldContents,E)}}))},s,i,n,(B,D)=>{_(B.qualifiedPathName.relPath,D)},B=>{const D=x[B.qualifiedPathName.relPath]||B.newContents;c(B.qualifiedPathName.relPath,B.oldContents,D)},B=>u(B.qualifiedPathName.relPath)]}class Co extends tt{constructor(t){super(),et(this,t,xo,Fo,K,{changedFiles:0,onApplyChanges:1,onOpenFile:2,pendingFiles:3,appliedFiles:4,isLoadingTreeView:5})}}function zn(r,t,e){const n=r.slice();return n[3]=t[e],n}function Ln(r){let t,e=dt(r[1].paths),n=[];for(let s=0;s<e.length;s+=1)n[s]=Mn(zn(r,e,s));return{c(){for(let s=0;s<n.length;s+=1)n[s].c();t=vt()},m(s,i){for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(s,i);h(s,t,i)},p(s,i){if(2&i){let o;for(e=dt(s[1].paths),o=0;o<e.length;o+=1){const l=zn(s,e,o);n[o]?n[o].p(l,i):(n[o]=Mn(l),n[o].c(),n[o].m(t.parentNode,t))}for(;o<n.length;o+=1)n[o].d(1);n.length=e.length}},d(s){s&&m(t),Mt(n,s)}}}function Mn(r){let t,e;return{c(){t=Ws("path"),v(t,"d",e=r[3]),v(t,"fill-rule","evenodd"),v(t,"clip-rule","evenodd")},m(n,s){h(n,t,s)},p(n,s){2&s&&e!==(e=n[3])&&v(t,"d",e)},d(n){n&&m(t)}}}function wo(r){let t,e=r[1]&&Ln(r);return{c(){t=Ws("svg"),e&&e.c(),v(t,"width","14"),v(t,"viewBox","0 0 20 20"),v(t,"fill","currentColor"),v(t,"class","svelte-10h4f31")},m(n,s){h(n,t,s),e&&e.m(t,null)},p(n,s){n[1]?e?e.p(n,s):(e=Ln(n),e.c(),e.m(t,null)):e&&(e.d(1),e=null)},d(n){n&&m(t),e&&e.d()}}}function vo(r){let t,e;return t=new Vt({props:{content:`This is a ${r[0]} change`,triggerOn:[re.Hover],$$slots:{default:[wo]},$$scope:{ctx:r}}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,[s]){const i={};1&s&&(i.content=`This is a ${n[0]} change`),66&s&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function yo(r,t,e){let n,{type:s}=t;const i={fix:{paths:["M6.56 1.14a.75.75 0 0 1 .177 1.045 3.989 3.989 0 0 0-.464.86c.185.17.382.329.59.473A3.993 3.993 0 0 1 10 2c1.272 0 2.405.594 3.137 1.518.208-.144.405-.302.59-.473a3.989 3.989 0 0 0-.464-.86.75.75 0 0 1 1.222-.869c.369.519.65 1.105.822 1.736a.75.75 0 0 1-.174.707 7.03 7.03 0 0 1-1.299 1.098A4 4 0 0 1 14 6c0 .52-.301.963-.723 1.187a6.961 6.961 0 0 1-1.158.486c.13.208.231.436.296.679 1.413-.174 2.779-.5 4.081-.96a19.655 19.655 0 0 0-.09-2.319.75.75 0 1 1 1.493-.146 21.239 21.239 0 0 1 .08 3.028.75.75 0 0 1-.482.667 20.873 20.873 0 0 1-5.153 1.249 2.521 2.521 0 0 1-.107.247 20.945 20.945 0 0 1 5.252 1.257.75.75 0 0 1 .482.74 20.945 20.945 0 0 1-.908 5.107.75.75 0 0 1-1.433-.444c.415-1.34.69-2.743.806-4.191-.495-.173-1-.327-1.512-.46.05.284.076.575.076.873 0 1.814-.517 3.312-1.426 4.37A4.639 4.639 0 0 1 10 19a4.639 4.639 0 0 1-3.574-1.63C5.516 16.311 5 14.813 5 13c0-.298.026-.59.076-.873-.513.133-1.017.287-1.512.46.116 1.448.39 2.85.806 4.191a.75.75 0 1 1-1.433.444 20.94 20.94 0 0 1-.908-5.107.75.75 0 0 1 .482-.74 20.838 20.838 0 0 1 5.252-1.257 2.493 2.493 0 0 1-.107-.247 20.874 20.874 0 0 1-5.153-1.249.75.75 0 0 1-.482-.667 21.342 21.342 0 0 1 .08-3.028.75.75 0 1 1 1.493.146 19.745 19.745 0 0 0-.09 2.319c1.302.46 2.668.786 4.08.96.066-.243.166-.471.297-.679a6.962 6.962 0 0 1-1.158-.486A1.348 1.348 0 0 1 6 6a4 4 0 0 1 .166-1.143 7.032 7.032 0 0 1-1.3-1.098.75.75 0 0 1-.173-.707 5.48 5.48 0 0 1 .822-1.736.75.75 0 0 1 1.046-.177Z"],color:"var(--ds-color-warning-9)"},feature:{paths:["M14 6a2.5 2.5 0 0 0-4-3 2.5 2.5 0 0 0-4 3H3.25C2.56 6 2 6.56 2 7.25v.5C2 8.44 2.56 9 3.25 9h6V6h1.5v3h6C17.44 9 18 8.44 18 7.75v-.5C18 6.56 17.44 6 16.75 6H14Zm-1-1.5a1 1 0 0 1-1 1h-1v-1a1 1 0 1 1 2 0Zm-6 0a1 1 0 0 0 1 1h1v-1a1 1 0 0 0-2 0Z","M9.25 10.5H3v4.75A2.75 2.75 0 0 0 5.75 18h3.5v-7.5ZM10.75 18v-7.5H17v4.75A2.75 2.75 0 0 1 14.25 18h-3.5Z"],color:"var(--ds-color-warning-9)"},refactor:{paths:["M8.157 2.176a1.5 1.5 0 0 0-1.147 0l-4.084 1.69A1.5 1.5 0 0 0 2 5.25v10.877a1.5 1.5 0 0 0 2.074 1.386l3.51-1.452 4.26 1.762a1.5 1.5 0 0 0 1.146 0l4.083-1.69A1.5 1.5 0 0 0 18 14.75V3.872a1.5 1.5 0 0 0-2.073-1.386l-3.51 1.452-4.26-1.762ZM7.58 5a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0v-6.5A.75.75 0 0 1 7.58 5Zm5.59 2.75a.75.75 0 0 0-1.5 0v6.5a.75.75 0 0 0 1.5 0v-6.5Z"],color:"var(--ds-color-warning-9)"},documentation:{paths:["M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm2.25 8.5a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Zm0 3a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Z"],color:"var(--ds-color-warning-9)"},style:{paths:["M15.993 1.385a1.87 1.87 0 0 1 2.623 2.622l-4.03 5.27a12.749 12.749 0 0 1-4.237 3.562 4.508 4.508 0 0 0-3.188-3.188 12.75 12.75 0 0 1 3.562-4.236l5.27-4.03ZM6 11a3 3 0 0 0-3 3 .5.5 0 0 1-.72.45.75.75 0 0 0-1.035.931A4.001 4.001 0 0 0 9 14.004V14a3.01 3.01 0 0 0-1.66-2.685A2.99 2.99 0 0 0 6 11Z"],color:"var(--ds-color-warning-9)"},test:{paths:["M8.5 3.528v4.644c0 .729-.29 1.428-.805 1.944l-1.217 1.216a8.75 8.75 0 0 1 3.55.621l.502.201a7.25 7.25 0 0 0 4.178.365l-2.403-2.403a2.75 2.75 0 0 1-.805-1.944V3.528a40.205 40.205 0 0 0-3 0Zm4.5.084.19.015a.75.75 0 1 0 .12-1.495 41.364 41.364 0 0 0-6.62 0 .75.75 0 0 0 .12 1.495L7 3.612v4.56c0 .331-.132.649-.366.883L2.6 13.09c-1.496 1.496-.817 4.15 1.403 4.475C5.961 17.852 7.963 18 10 18s4.039-.148 5.997-.436c2.22-.325 2.9-2.979 1.403-4.475l-4.034-4.034A1.25 1.25 0 0 1 13 8.172v-4.56Z"],color:"var(--ds-color-warning-9)"},chore:{paths:["m6.75.98-.884.883a1.25 1.25 0 1 0 1.768 0L6.75.98ZM13.25.98l-.884.883a1.25 1.25 0 1 0 1.768 0L13.25.98ZM10 .98l.884.883a1.25 1.25 0 1 1-1.768 0L10 .98ZM7.5 5.75a.75.75 0 0 0-1.5 0v.464c-1.179.304-2 1.39-2 2.622v.094c.1-.02.202-.038.306-.052A42.867 42.867 0 0 1 10 8.5c1.93 0 3.83.129 5.694.378.104.014.206.032.306.052v-.094c0-1.232-.821-2.317-2-2.622V5.75a.75.75 0 0 0-1.5 0v.318a45.645 45.645 0 0 0-1.75-.062V5.75a.75.75 0 0 0-1.5 0v.256c-.586.01-1.17.03-1.75.062V5.75ZM4.505 10.365A41.36 41.36 0 0 1 10 10c1.863 0 3.697.124 5.495.365C16.967 10.562 18 11.838 18 13.28v.693a3.72 3.72 0 0 1-1.665-.393 5.222 5.222 0 0 0-4.67 0 3.722 3.722 0 0 1-3.33 0 5.222 5.222 0 0 0-4.67 0A3.72 3.72 0 0 1 2 13.972v-.693c0-1.441 1.033-2.717 2.505-2.914ZM15.665 14.92a5.22 5.22 0 0 0 2.335.552V16.5a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 2 16.5v-1.028c.8 0 1.6-.184 2.335-.551a3.722 3.722 0 0 1 3.33 0c1.47.735 3.2.735 4.67 0a3.722 3.722 0 0 1 3.33 0Z"],color:"var(--ds-color-warning-9)"},performance:{paths:["M4.606 12.97a.75.75 0 0 1-.134 1.051 2.494 2.494 0 0 0-.93 2.437 2.494 2.494 0 0 0 2.437-.93.75.75 0 1 1 1.186.918 3.995 3.995 0 0 1-4.482 1.332.75.75 0 0 1-.461-.461 3.994 3.994 0 0 1 1.332-4.482.75.75 0 0 1 1.052.134Z","M5.752 12A13.07 13.07 0 0 0 8 14.248v4.002c0 .414.336.75.75.75a5 5 0 0 0 4.797-6.414 12.984 12.984 0 0 0 5.45-10.848.75.75 0 0 0-.735-.735 12.984 12.984 0 0 0-10.849 5.45A5 5 0 0 0 1 11.25c.001.414.337.75.751.75h4.002ZM13 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"],color:"var(--ds-color-warning-9)"},revert:{paths:["M7.50043 1.37598C7.2023 1.37598 6.91637 1.49431 6.70543 1.70499L6.70521 1.70521L1.70521 6.70521L1.70499 6.70543C1.49431 6.91637 1.37598 7.2023 1.37598 7.50043C1.37598 7.79855 1.49431 8.08449 1.70499 8.29543L1.70521 8.29565L6.69987 13.2903C6.80149 13.3974 6.92322 13.4835 7.05815 13.5436C7.19615 13.6051 7.34512 13.6382 7.49617 13.6408C7.64723 13.6435 7.79727 13.6157 7.93735 13.5591C8.07744 13.5026 8.20469 13.4183 8.31151 13.3115C8.41834 13.2047 8.50256 13.0774 8.55914 12.9374C8.61572 12.7973 8.64351 12.6472 8.64084 12.4962C8.63818 12.3451 8.60511 12.1961 8.54363 12.0581C8.48351 11.9232 8.39743 11.8015 8.29032 11.6999L5.21587 8.62543H12.5004C13.0093 8.62543 13.5132 8.72566 13.9833 8.92039C14.4535 9.11513 14.8806 9.40056 15.2405 9.76039C15.6003 10.1202 15.8857 10.5474 16.0805 11.0175C16.2752 11.4877 16.3754 11.9916 16.3754 12.5004C16.3754 13.0093 16.2752 13.5132 16.0805 13.9833C15.8857 14.4535 15.6003 14.8806 15.2405 15.2405C14.8806 15.6003 14.4535 15.8857 13.9833 16.0805C13.5132 16.2752 13.0093 16.3754 12.5004 16.3754H10.0004C9.70206 16.3754 9.41591 16.494 9.20493 16.7049C8.99395 16.9159 8.87543 17.2021 8.87543 17.5004C8.87543 17.7988 8.99395 18.0849 9.20493 18.2959C9.41591 18.5069 9.70206 18.6254 10.0004 18.6254H12.5004C14.1249 18.6254 15.6828 17.9801 16.8315 16.8315C17.9801 15.6828 18.6254 14.1249 18.6254 12.5004C18.6254 10.876 17.9801 9.31806 16.8315 8.1694C15.6828 7.02074 14.1249 6.37543 12.5004 6.37543H5.21587L8.29565 3.29565L8.29587 3.29543C8.50654 3.08449 8.62488 2.79855 8.62488 2.50043C8.62488 2.2023 8.50654 1.91636 8.29587 1.70543L8.29543 1.70499C8.08449 1.49431 7.79855 1.37598 7.50043 1.37598Z","M7.712 4.818A1.5 1.5 0 0 1 10 6.095v2.972c.104-.13.234-.248.389-.343l6.323-3.906A1.5 1.5 0 0 1 19 6.095v7.81a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.505 1.505 0 0 1-.389-.344v2.973a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.5 1.5 0 0 1 0-2.552l6.323-3.906Z"],color:"var(--ds-color-warning-9)"},other:{paths:["M2 4.25C2 3.65326 2.23705 3.08097 2.65901 2.65901C3.08097 2.23705 3.65326 2 4.25 2H6.75C7.34674 2 7.91903 2.23705 8.34099 2.65901C8.76295 3.08097 9 3.65326 9 4.25V6.75C9 7.34674 8.76295 7.91903 8.34099 8.34099C7.91903 8.76295 7.34674 9 6.75 9H4.25C3.65326 9 3.08097 8.76295 2.65901 8.34099C2.23705 7.91903 2 7.34674 2 6.75V4.25ZM15.25 11.75C15.25 11.5511 15.171 11.3603 15.0303 11.2197C14.8897 11.079 14.6989 11 14.5 11C14.3011 11 14.1103 11.079 13.9697 11.2197C13.829 11.3603 13.75 11.5511 13.75 11.75V13.75H11.75C11.5511 13.75 11.3603 13.829 11.2197 13.9697C11.079 14.1103 11 14.3011 11 14.5C11 14.6989 11.079 14.8897 11.2197 15.0303C11.3603 15.171 11.5511 15.25 11.75 15.25H13.75V17.25C13.75 17.4489 13.829 17.6397 13.9697 17.7803C14.1103 17.921 14.3011 18 14.5 18C14.6989 18 14.8897 17.921 15.0303 17.7803C15.171 17.6397 15.25 17.4489 15.25 17.25V15.25H17.25C17.4489 15.25 17.6397 15.171 17.7803 15.0303C17.921 14.8897 18 14.6989 18 14.5C18 14.3011 17.921 14.1103 17.7803 13.9697C17.6397 13.829 17.4489 13.75 17.25 13.75H15.25V11.75Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M9 14.5C9 16.433 7.433 18 5.5 18C3.567 18 2 16.433 2 14.5C2 12.567 3.567 11 5.5 11C7.433 11 9 12.567 9 14.5Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M 9 14.5 A 3.5 3.5 0 1 1 2 14.5 A 3.5 3.5 0 1 1 9 14.5 Z"],color:"var(--ds-color-warning-9)"}};return r.$$set=o=>{"type"in o&&e(0,s=o.type)},r.$$.update=()=>{1&r.$$.dirty&&e(1,n=i[s]??i.other)},[s,n]}class ko extends tt{constructor(t){super(),et(this,t,yo,vo,K,{type:0})}}function Rn(r,t){return`${r}:${t}`}const Ao=(r,t)=>{let e=null,n=null,s=null,i=!1;function o(){s&&cancelAnimationFrame(s),s=requestAnimationFrame(()=>{const{path:u,onCollapseStateChange:d}=t;if(i)return void(s=null);const p=Array.from(document.querySelectorAll(`[data-description-id^="${u}:"]`));let C=!1;for(const x of p)if(x!==r&&l(r,x)){C=!0;break}C&&(i=!0),d&&d(C),s=null})}function l(u,d){const p=u.getBoundingClientRect(),C=d.getBoundingClientRect();return!(p.bottom<=C.top||C.bottom<=p.top)}function a(){c(),e=new MutationObserver(()=>{o()});const u=r.closest(".descriptions")||document.body;e.observe(u,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["style","data-description-id"]}),window.ResizeObserver&&(n=new ResizeObserver(()=>{o()}),n.observe(r)),window.addEventListener("resize",o),window.addEventListener("scroll",o)}function c(){e&&(e.disconnect(),e=null),n&&(n.disconnect(),n=null),s&&(cancelAnimationFrame(s),s=null),window.removeEventListener("resize",o),window.removeEventListener("scroll",o)}return document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>{a(),o()}):requestAnimationFrame(()=>{a(),o()}),{update:u=>{t=u,i=!1,o()},destroy:c}};function bo(r){let t,e,n,s,i,o,l,a,c,u,d,p,C;return n=new be({props:{markdown:r[0].text}}),o=new be({props:{markdown:r[7]}}),{c(){t=b("div"),e=b("div"),z(n.$$.fragment),s=I(),i=b("div"),z(o.$$.fragment),l=I(),a=b("div"),a.textContent="hover to expand",v(e,"class","c-diff-description__content svelte-wweiw1"),v(a,"class","c-diff-description__expand-hint svelte-wweiw1"),v(i,"class","c-diff-description__truncated-content svelte-wweiw1"),v(t,"class","c-diff-description svelte-wweiw1"),Nt(t,"top",r[1]+"px"),v(t,"data-description-id",c=Rn(r[2],r[3])),v(t,"role","region"),v(t,"aria-label","Code diff description"),Ft(t,"c-diff-description__collapsed",r[6]&&!r[5]),Ft(t,"c-diff-description__hovered",r[5])},m(x,$){h(x,t,$),R(t,e),L(n,e,null),R(t,s),R(t,i),L(o,i,null),R(i,l),R(i,a),r[10](t),d=!0,p||(C=[Zs(u=Ao.call(null,t,{path:r[2],onCollapseStateChange:r[11]})),Qt(t,"mouseenter",r[12]),Qt(t,"mouseleave",r[13])],p=!0)},p(x,[$]){const k={};1&$&&(k.markdown=x[0].text),n.$set(k);const _={};128&$&&(_.markdown=x[7]),o.$set(_),(!d||2&$)&&Nt(t,"top",x[1]+"px"),(!d||12&$&&c!==(c=Rn(x[2],x[3])))&&v(t,"data-description-id",c),u&&We(u.update)&&68&$&&u.update.call(null,{path:x[2],onCollapseStateChange:x[11]}),(!d||96&$)&&Ft(t,"c-diff-description__collapsed",x[6]&&!x[5]),(!d||32&$)&&Ft(t,"c-diff-description__hovered",x[5])},i(x){d||(f(n.$$.fragment,x),f(o.$$.fragment,x),d=!0)},o(x){g(n.$$.fragment,x),g(o.$$.fragment,x),d=!1},d(x){x&&m(t),M(n),M(o),r[10](null),p=!1,ze(C)}}}function Eo(r){const t=document.createElement("canvas").getContext("2d");return t?t.measureText(r).width:8*r.length}function _o(r,t,e){let n,s,{description:i}=t,{position:o}=t,{fileId:l}=t,{index:a}=t,c=!1,u=!1,d=0;const p=Hi($=>{e(5,c=$)},100);function C(){if(s){const $=s.getBoundingClientRect();e(9,d=$.width-128)}}let x=null;return Gt(()=>{s&&typeof ResizeObserver<"u"&&(x=new ResizeObserver(()=>{C()}),x.observe(s),C())}),Le(()=>{x&&x.disconnect()}),r.$$set=$=>{"description"in $&&e(0,i=$.description),"position"in $&&e(1,o=$.position),"fileId"in $&&e(2,l=$.fileId),"index"in $&&e(3,a=$.index)},r.$$.update=()=>{16&r.$$.dirty&&s&&C(),513&r.$$.dirty&&e(7,n=(()=>{const $=i.text.split(`
`)[0].split(" ");let k="";if(d<=0)for(const _ of $){const B=k+(k?" ":"")+_;if(B.length>30)break;k=B}else for(const _ of $){const B=k+(k?" ":"")+_;if(Eo(B+"...")>d)break;k=B}return k+"..."})())},[i,o,l,a,s,c,u,n,p,d,function($){bt[$?"unshift":"push"](()=>{s=$,e(4,s)})},$=>e(6,u=$),$=>{p.cancel(),e(5,c=!0),$.stopPropagation()},$=>{p(!1),$.stopPropagation()}]}class Bo extends tt{constructor(t){super(),et(this,t,_o,bo,K,{description:0,position:1,fileId:2,index:3})}}function On(r,t,e){const n=r.slice();return n[48]=t[e],n[50]=e,n}function Nn(r){let t,e,n,s,i;e=new pe({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Mo]},$$scope:{ctx:r}}}),e.$on("click",r[25]);let o=dt(r[1]),l=[];for(let c=0;c<o.length;c+=1)l[c]=qn(On(r,o,c));const a=c=>g(l[c],1,1,()=>{l[c]=null});return{c(){t=b("div"),z(e.$$.fragment),n=I(),s=b("div");for(let c=0;c<l.length;c+=1)l[c].c();v(t,"class","toggle-button svelte-1r29xbx"),v(s,"class","descriptions svelte-1r29xbx"),Nt(s,"transform","translateY("+-r[4]+"px)")},m(c,u){h(c,t,u),L(e,t,null),h(c,n,u),h(c,s,u);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(s,null);i=!0},p(c,u){const d={};if(1&u[0]|1048576&u[1]&&(d.$$scope={dirty:u,ctx:c}),e.$set(d),1570&u[0]){let p;for(o=dt(c[1]),p=0;p<o.length;p+=1){const C=On(c,o,p);l[p]?(l[p].p(C,u),f(l[p],1)):(l[p]=qn(C),l[p].c(),f(l[p],1),l[p].m(s,null))}for(j(),p=o.length;p<l.length;p+=1)a(p);W()}(!i||16&u[0])&&Nt(s,"transform","translateY("+-c[4]+"px)")},i(c){if(!i){f(e.$$.fragment,c);for(let u=0;u<o.length;u+=1)f(l[u]);i=!0}},o(c){g(e.$$.fragment,c),l=l.filter(Boolean);for(let u=0;u<l.length;u+=1)g(l[u]);i=!1},d(c){c&&(m(t),m(n),m(s)),M(e),Mt(l,c)}}}function zo(r){let t,e;return t=new $e({props:{icon:"book"}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function Lo(r){let t,e;return t=new $e({props:{icon:"x"}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function Mo(r){let t,e,n,s;const i=[Lo,zo],o=[];function l(a,c){return a[0]?0:1}return t=l(r),e=o[t]=i[t](r),{c(){e.c(),n=vt()},m(a,c){o[t].m(a,c),h(a,n,c),s=!0},p(a,c){let u=t;t=l(a),t!==u&&(j(),g(o[u],1,1,()=>{o[u]=null}),W(),e=o[t],e||(e=o[t]=i[t](a),e.c()),f(e,1),e.m(n.parentNode,n))},i(a){s||(f(e),s=!0)},o(a){g(e),s=!1},d(a){a&&m(n),o[t].d(a)}}}function qn(r){let t,e;return t=new Bo({props:{description:r[48],position:r[5][r[50]]||r[9](r[48]),fileId:r[10],index:r[50]}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};2&s[0]&&(i.description=n[48]),34&s[0]&&(i.position=n[5][n[50]]||n[9](n[48])),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function Ro(r){let t,e,n,s,i=r[1].length>0&&Nn(r);return{c(){t=b("div"),e=b("div"),n=I(),i&&i.c(),v(e,"class","editor-container svelte-1r29xbx"),Nt(e,"height",r[3]+"px"),v(t,"class","monaco-diff-container svelte-1r29xbx"),Ft(t,"monaco-diff-container-with-descriptions",r[1].length>0&&r[0])},m(o,l){h(o,t,l),R(t,e),r[24](e),R(t,n),i&&i.m(t,null),s=!0},p(o,l){(!s||8&l[0])&&Nt(e,"height",o[3]+"px"),o[1].length>0?i?(i.p(o,l),2&l[0]&&f(i,1)):(i=Nn(o),i.c(),f(i,1),i.m(t,null)):i&&(j(),g(i,1,1,()=>{i=null}),W()),(!s||3&l[0])&&Ft(t,"monaco-diff-container-with-descriptions",o[1].length>0&&o[0])},i(o){s||(f(i),s=!0)},o(o){g(i),s=!1},d(o){o&&m(t),r[24](null),i&&i.d()}}}function Oo(r,t,e){let n,s,i;const o=Hs();let{originalCode:l=""}=t,{modifiedCode:a=""}=t,{path:c}=t,{descriptions:u=[]}=t,{lineOffset:d=0}=t,{extraPrefixLines:p=[]}=t,{extraSuffixLines:C=[]}=t,{theme:x}=t,{areDescriptionsVisible:$=!0}=t,{isNewFile:k=!1}=t,{isDeletedFile:_=!1}=t;const B=Ge.getContext().monaco;let D,F,w,E;Rt(r,B,A=>e(23,n=A));let O,S=[];const T=Je();let Z,Q=Kt(0);Rt(r,Q,A=>e(4,s=A));let nt=k?20*a.split(`
`).length+40:100;const ct=n?n.languages.getLanguages().map(A=>A.id):[];function at(A,y){var q,V;if(y){const P=(q=y.split(".").pop())==null?void 0:q.toLowerCase();if(P){const H=(V=n==null?void 0:n.languages.getLanguages().find(Y=>{var X;return(X=Y.extensions)==null?void 0:X.includes("."+P)}))==null?void 0:V.id;if(H&&ct.includes(H))return H}}return"plaintext"}const lt=Kt({});Rt(r,lt,A=>e(5,i=A));let J=null;function ft(){if(!D)return;S=S.filter(q=>(q.dispose(),!1));const A=D.getOriginalEditor(),y=D.getModifiedEditor();S.push(A.onDidScrollChange(()=>{we(Q,s=A.getScrollTop(),s)}),y.onDidScrollChange(()=>{we(Q,s=y.getScrollTop(),s)}))}function mt(){if(!D||!O)return;const A=D.getOriginalEditor(),y=D.getModifiedEditor();S.push(y.onDidContentSizeChange(()=>T.requestLayout()),A.onDidContentSizeChange(()=>T.requestLayout()),D.onDidUpdateDiff(()=>T.requestLayout()),y.onDidChangeHiddenAreas(()=>T.requestLayout()),A.onDidChangeHiddenAreas(()=>T.requestLayout()),y.onDidLayoutChange(()=>T.requestLayout()),A.onDidLayoutChange(()=>T.requestLayout()),y.onDidFocusEditorWidget(()=>{kt(!0)}),A.onDidFocusEditorWidget(()=>{kt(!0)}),y.onDidBlurEditorWidget(()=>{kt(!1)}),A.onDidBlurEditorWidget(()=>{kt(!1)}),y.onDidChangeModelContent(()=>{Ct=!0,Ot=Date.now();const q=(E==null?void 0:E.getValue())||"";if(q===a)return;const V=q.replace(p.join(""),"").replace(C.join(""),"");o("codeChange",{modifiedCode:V});const P=setTimeout(()=>{Ct=!1},500);S.push({dispose:()=>clearTimeout(P)})})),function(){!O||!D||(J&&clearTimeout(J),J=setTimeout(()=>{if(!O.__hasClickListener){const q=V=>{const P=V.target;P&&(P.closest('[title="Show Unchanged Region"]')||P.closest('[title="Hide Unchanged Region"]'))&&At()};O.addEventListener("click",q),e(2,O.__hasClickListener=!0,O),S.push({dispose:()=>{O.removeEventListener("click",q)}})}D&&S.push(D.onDidUpdateDiff(()=>{At()}))},300))}()}Le(()=>{D==null||D.dispose(),F==null||F.dispose(),w==null||w.dispose(),E==null||E.dispose(),S.forEach(A=>A.dispose()),J&&clearTimeout(J),Z==null||Z()});let it=null;function At(){it&&clearTimeout(it),it=setTimeout(()=>{T.requestLayout(),it=null},100),it&&S.push({dispose:()=>{it&&(clearTimeout(it),it=null)}})}function yt(A,y,q,V=[],P=[]){if(!n)return void console.error("Monaco not loaded. Diff view cannot be updated.");w==null||w.dispose(),E==null||E.dispose(),y=y||"",q=q||"";const H=V.join(""),Y=P.join("");if(y=k?q.split(`
`).map(()=>" ").join(`
`):H+y+Y,q=H+q+Y,w=n.editor.createModel(y,void 0,A!==void 0?n.Uri.parse("file://"+A+`#${crypto.randomUUID()}`):void 0),_&&(q=q.split(`
`).map(()=>" ").join(`
`)),e(22,E=n.editor.createModel(q,void 0,A!==void 0?n.Uri.parse("file://"+A+`#${crypto.randomUUID()}`):void 0)),D){D.setModel({original:w,modified:E});const X=D.getOriginalEditor();X&&X.updateOptions({lineNumbers:"off"}),ft(),J&&clearTimeout(J),J=setTimeout(()=>{mt(),J=null},300)}}Gt(()=>{if(n)if(k){e(21,F=n.editor.create(O,{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:x,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:V=>`${d-p.length+V}`}));const A=at(0,c);e(22,E=n.editor.createModel(a,A,c!==void 0?n.Uri.parse("file://"+c+`#${crypto.randomUUID()}`):void 0)),F.setModel(E),S.push(F.onDidChangeModelContent(()=>{Ct=!0,Ot=Date.now();const V=(E==null?void 0:E.getValue())||"";if(V===a)return;o("codeChange",{modifiedCode:V});const P=setTimeout(()=>{Ct=!1},500);S.push({dispose:()=>clearTimeout(P)})})),S.push(F.onDidFocusEditorWidget(()=>{F==null||F.updateOptions({scrollbar:{handleMouseWheel:!0}})}),F.onDidBlurEditorWidget(()=>{F==null||F.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const y=F.getContentHeight();e(3,nt=Math.max(y,60));const q=setTimeout(()=>{F==null||F.layout()},0);S.push({dispose:()=>clearTimeout(q)})}else e(20,D=n.editor.createDiffEditor(O,{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:x,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:A=>`${d-p.length+A}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),Z&&Z(),Z=T.registerEditor({editor:D,updateHeight:Et,id:`monaco-diff-${crypto.randomUUID().slice(0,8)}`}),yt(c,l,a,p,C),ft(),mt(),J&&clearTimeout(J),J=setTimeout(()=>{T.requestLayout(),J=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let Ct=!1,Ot=0;function pt(A,y=!0){return D?(y?D.getModifiedEditor():D.getOriginalEditor()).getTopForLineNumber(A):18*A}function Et(){if(!D)return;const A=D.getModel(),y=A==null?void 0:A.original,q=A==null?void 0:A.modified;if(!y||!q)return;const V=D.getOriginalEditor(),P=D.getModifiedEditor(),H=D.getLineChanges()||[];let Y;if(H.length===0){const X=V.getContentHeight(),G=P.getContentHeight();Y=Math.max(100,X,G)}else{let X=0,G=0;for(const wt of H)wt.originalEndLineNumber>0&&(X=Math.max(X,wt.originalEndLineNumber)),wt.modifiedEndLineNumber>0&&(G=Math.max(G,wt.modifiedEndLineNumber));X=Math.min(X+3,y.getLineCount()),G=Math.min(G+3,q.getLineCount());const ut=V.getTopForLineNumber(X),ht=P.getTopForLineNumber(G);Y=Math.max(ut,ht)+60}e(3,nt=Math.min(Y,2e4)),D.layout(),Jt()}function kt(A){if(!D)return;const y=D.getOriginalEditor(),q=D.getModifiedEditor();y.updateOptions({scrollbar:{handleMouseWheel:A}}),q.updateOptions({scrollbar:{handleMouseWheel:A}})}function U(A){if(!D)return F?F.getTopForLineNumber(A.range.start+1):0;const y=D.getModel(),q=y==null?void 0:y.original,V=y==null?void 0:y.modified;if(!q||!V)return 0;const P=pt(A.range.start+1,!1),H=pt(A.range.start+1,!0);return P&&!H?P:!P&&H?H:Math.min(P,H)}function Jt(){if(!D&&!F||u.length===0)return;const A={};u.forEach((y,q)=>{A[q]=U(y)}),function(y,q=50){const V=Object.keys(y).sort((P,H)=>y[Number(P)]-y[Number(H)]);for(let P=0;P<V.length-1;P++){const H=Number(V[P]),Y=y[H];y[H+1]-Y<q&&(y[Number(V[P+1])]=Y+q)}}(A),lt.set(A)}const Yt=crypto.randomUUID();return r.$$set=A=>{"originalCode"in A&&e(11,l=A.originalCode),"modifiedCode"in A&&e(12,a=A.modifiedCode),"path"in A&&e(13,c=A.path),"descriptions"in A&&e(1,u=A.descriptions),"lineOffset"in A&&e(14,d=A.lineOffset),"extraPrefixLines"in A&&e(15,p=A.extraPrefixLines),"extraSuffixLines"in A&&e(16,C=A.extraSuffixLines),"theme"in A&&e(17,x=A.theme),"areDescriptionsVisible"in A&&e(0,$=A.areDescriptionsVisible),"isNewFile"in A&&e(18,k=A.isNewFile),"isDeletedFile"in A&&e(19,_=A.isDeletedFile)},r.$$.update=()=>{if(16103424&r.$$.dirty[0]&&(A=a,!(Ct||Date.now()-Ot<1e3||E&&E.getValue()===p.join("")+A+C.join(""))))if(k&&F){if(E)E.setValue(a);else{const y=at(0,c);n&&e(22,E=n.editor.createModel(a,y,c!==void 0?n.Uri.parse("file://"+c+`#${crypto.randomUUID()}`):void 0)),E&&F.setModel(E)}e(3,nt=20*a.split(`
`).length+40),F.layout()}else!k&&D&&(yt(c,l,a,p,C),T.requestLayout());var A;if(3145730&r.$$.dirty[0]&&(D||F)&&u.length>0&&Jt(),2363392&r.$$.dirty[0]&&k&&a&&F){const y=F.getContentHeight();e(3,nt=Math.max(y,60)),F.layout()}},[$,u,O,nt,s,i,B,Q,lt,U,Yt,l,a,c,d,p,C,x,k,_,D,F,E,n,function(A){bt[A?"unshift":"push"](()=>{O=A,e(2,O)})},()=>e(0,$=!$)]}class No extends tt{constructor(t){super(),et(this,t,Oo,Ro,K,{originalCode:11,modifiedCode:12,path:13,descriptions:1,lineOffset:14,extraPrefixLines:15,extraSuffixLines:16,theme:17,areDescriptionsVisible:0,isNewFile:18,isDeletedFile:19},null,[-1,-1])}}const qo=Symbol("focusedPath");function Sn(r){return`file-diff-${Zt(r)}`}function So(r){let t,e,n;function s(o){r[41](o)}let i={path:r[3],originalCode:r[0].originalCode,modifiedCode:r[6],theme:r[15],descriptions:r[4],isNewFile:r[21],isDeletedFile:r[20]};return r[1]!==void 0&&(i.areDescriptionsVisible=r[1]),t=new No({props:i}),bt.push(()=>ee(t,"areDescriptionsVisible",s)),t.$on("codeChange",r[26]),{c(){z(t.$$.fragment)},m(o,l){L(t,o,l),n=!0},p(o,l){const a={};8&l[0]&&(a.path=o[3]),1&l[0]&&(a.originalCode=o[0].originalCode),64&l[0]&&(a.modifiedCode=o[6]),32768&l[0]&&(a.theme=o[15]),16&l[0]&&(a.descriptions=o[4]),2097152&l[0]&&(a.isNewFile=o[21]),1048576&l[0]&&(a.isDeletedFile=o[20]),!e&&2&l[0]&&(e=!0,a.areDescriptionsVisible=o[1],ne(()=>e=!1)),t.$set(a)},i(o){n||(f(t.$$.fragment,o),n=!0)},o(o){g(t.$$.fragment,o),n=!1},d(o){M(t,o)}}}function To(r){let t,e,n;return e=new $t({props:{size:1,$$slots:{default:[Uo]},$$scope:{ctx:r}}}),{c(){t=b("div"),z(e.$$.fragment),v(t,"class","too-large-message svelte-1536g7w")},m(s,i){h(s,t,i),L(e,t,null),n=!0},p(s,i){const o={};5888&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function Po(r){let t,e,n;return e=new $t({props:{$$slots:{default:[jo]},$$scope:{ctx:r}}}),{c(){t=b("div"),z(e.$$.fragment),v(t,"class","binary-file-message svelte-1536g7w")},m(s,i){h(s,t,i),L(e,t,null),n=!0},p(s,i){const o={};2101632&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function Io(r){let t,e,n,s;const i=[Qo,Wo],o=[];function l(a,c){return a[8]?0:a[6]?1:-1}return~(e=l(r))&&(n=o[e]=i[e](r)),{c(){t=b("div"),n&&n.c(),v(t,"class","image-container svelte-1536g7w")},m(a,c){h(a,t,c),~e&&o[e].m(t,null),s=!0},p(a,c){let u=e;e=l(a),e===u?~e&&o[e].p(a,c):(n&&(j(),g(o[u],1,1,()=>{o[u]=null}),W()),~e?(n=o[e],n?n.p(a,c):(n=o[e]=i[e](a),n.c()),f(n,1),n.m(t,null)):n=null)},i(a){s||(f(n),s=!0)},o(a){g(n),s=!1},d(a){a&&m(t),~e&&o[e].d()}}}function Uo(r){let t,e,n,s,i,o,l,a=ot(r[12])+"",c=(r[8]?r[10]:r[9])+"";return{c(){t=N('File "'),e=N(a),n=N('" is too large to display a diff (size: '),s=N(c),i=N(" bytes, max: "),o=N(oi),l=N(" bytes).")},m(u,d){h(u,t,d),h(u,e,d),h(u,n,d),h(u,s,d),h(u,i,d),h(u,o,d),h(u,l,d)},p(u,d){4096&d[0]&&a!==(a=ot(u[12])+"")&&rt(e,a),1792&d[0]&&c!==(c=(u[8]?u[10]:u[9])+"")&&rt(s,c)},d(u){u&&(m(t),m(e),m(n),m(s),m(i),m(o),m(l))}}}function Vo(r){let t,e,n,s=ot(r[12])+"";return{c(){t=N("Binary file modified: "),e=N(s),n=N(".")},m(i,o){h(i,t,o),h(i,e,o),h(i,n,o)},p(i,o){4096&o[0]&&s!==(s=ot(i[12])+"")&&rt(e,s)},d(i){i&&(m(t),m(e),m(n))}}}function Zo(r){let t,e,n,s=ot(r[12])+"";return{c(){t=N("Binary file deleted: "),e=N(s),n=N(".")},m(i,o){h(i,t,o),h(i,e,o),h(i,n,o)},p(i,o){4096&o[0]&&s!==(s=ot(i[12])+"")&&rt(e,s)},d(i){i&&(m(t),m(e),m(n))}}}function Ho(r){let t,e,n,s=ot(r[12])+"";return{c(){t=N("Binary file added: "),e=N(s),n=N(".")},m(i,o){h(i,t,o),h(i,e,o),h(i,n,o)},p(i,o){4096&o[0]&&s!==(s=ot(i[12])+"")&&rt(e,s)},d(i){i&&(m(t),m(e),m(n))}}}function jo(r){let t;function e(i,o){return i[21]||i[7]?Ho:i[8]?Zo:Vo}let n=e(r),s=n(r);return{c(){s.c(),t=N(`
            No text preview available.`)},m(i,o){s.m(i,o),h(i,t,o)},p(i,o){n===(n=e(i))&&s?s.p(i,o):(s.d(1),s=n(i),s&&(s.c(),s.m(t.parentNode,t)))},d(i){i&&m(t),s.d(i)}}}function Wo(r){let t,e,n,s,i,o,l,a;t=new $t({props:{class:"image-info-text",$$slots:{default:[Yo]},$$scope:{ctx:r}}});let c=r[0].originalCode&&r[6]!==r[0].originalCode&&!r[21]&&Tn(r);return{c(){z(t.$$.fragment),e=I(),n=b("img"),o=I(),c&&c.c(),l=vt(),qt(n.src,s="data:"+r[19]+";base64,"+btoa(r[6]))||v(n,"src",s),v(n,"alt",i="Current "+ot(r[12])),v(n,"class","image-preview svelte-1536g7w")},m(u,d){L(t,u,d),h(u,e,d),h(u,n,d),h(u,o,d),c&&c.m(u,d),h(u,l,d),a=!0},p(u,d){const p={};2101376&d[0]|16384&d[1]&&(p.$$scope={dirty:d,ctx:u}),t.$set(p),(!a||524352&d[0]&&!qt(n.src,s="data:"+u[19]+";base64,"+btoa(u[6])))&&v(n,"src",s),(!a||4096&d[0]&&i!==(i="Current "+ot(u[12])))&&v(n,"alt",i),u[0].originalCode&&u[6]!==u[0].originalCode&&!u[21]?c?(c.p(u,d),2097217&d[0]&&f(c,1)):(c=Tn(u),c.c(),f(c,1),c.m(l.parentNode,l)):c&&(j(),g(c,1,1,()=>{c=null}),W())},i(u){a||(f(t.$$.fragment,u),f(c),a=!0)},o(u){g(t.$$.fragment,u),g(c),a=!1},d(u){u&&(m(e),m(n),m(o),m(l)),M(t,u),c&&c.d(u)}}}function Qo(r){let t,e,n,s;t=new $t({props:{class:"image-info-text",$$slots:{default:[Ko]},$$scope:{ctx:r}}});let i=r[0].originalCode&&Pn(r);return{c(){z(t.$$.fragment),e=I(),i&&i.c(),n=vt()},m(o,l){L(t,o,l),h(o,e,l),i&&i.m(o,l),h(o,n,l),s=!0},p(o,l){const a={};4096&l[0]|16384&l[1]&&(a.$$scope={dirty:l,ctx:o}),t.$set(a),o[0].originalCode?i?(i.p(o,l),1&l[0]&&f(i,1)):(i=Pn(o),i.c(),f(i,1),i.m(n.parentNode,n)):i&&(j(),g(i,1,1,()=>{i=null}),W())},i(o){s||(f(t.$$.fragment,o),f(i),s=!0)},o(o){g(t.$$.fragment,o),g(i),s=!1},d(o){o&&(m(e),m(n)),M(t,o),i&&i.d(o)}}}function Go(r){let t;return{c(){t=N("Image modified")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function Jo(r){let t;return{c(){t=N("New image added")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function Yo(r){let t,e,n=ot(r[12])+"";function s(l,a){return l[21]||l[7]?Jo:Go}let i=s(r),o=i(r);return{c(){o.c(),t=N(": "),e=N(n)},m(l,a){o.m(l,a),h(l,t,a),h(l,e,a)},p(l,a){i!==(i=s(l))&&(o.d(1),o=i(l),o&&(o.c(),o.m(t.parentNode,t))),4096&a[0]&&n!==(n=ot(l[12])+"")&&rt(e,n)},d(l){l&&(m(t),m(e)),o.d(l)}}}function Tn(r){let t,e,n,s,i,o;return t=new $t({props:{class:"image-info-text",$$slots:{default:[Xo]},$$scope:{ctx:r}}}),{c(){z(t.$$.fragment),e=I(),n=b("img"),qt(n.src,s="data:"+jt(r[3])+";base64,"+btoa(r[0].originalCode))||v(n,"src",s),v(n,"alt",i="Original "+ot(r[12])),v(n,"class","image-preview image-preview--previous svelte-1536g7w")},m(l,a){L(t,l,a),h(l,e,a),h(l,n,a),o=!0},p(l,a){const c={};16384&a[1]&&(c.$$scope={dirty:a,ctx:l}),t.$set(c),(!o||9&a[0]&&!qt(n.src,s="data:"+jt(l[3])+";base64,"+btoa(l[0].originalCode)))&&v(n,"src",s),(!o||4096&a[0]&&i!==(i="Original "+ot(l[12])))&&v(n,"alt",i)},i(l){o||(f(t.$$.fragment,l),o=!0)},o(l){g(t.$$.fragment,l),o=!1},d(l){l&&(m(e),m(n)),M(t,l)}}}function Xo(r){let t;return{c(){t=N("Previous version:")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function Ko(r){let t,e,n=ot(r[12])+"";return{c(){t=N("Image deleted: "),e=N(n)},m(s,i){h(s,t,i),h(s,e,i)},p(s,i){4096&i[0]&&n!==(n=ot(s[12])+"")&&rt(e,n)},d(s){s&&(m(t),m(e))}}}function Pn(r){let t,e,n,s,i,o;return t=new $t({props:{class:"image-info-text",$$slots:{default:[tl]},$$scope:{ctx:r}}}),{c(){z(t.$$.fragment),e=I(),n=b("img"),qt(n.src,s="data:"+jt(r[3])+";base64,"+btoa(r[0].originalCode))||v(n,"src",s),v(n,"alt",i="Original "+ot(r[12])),v(n,"class","image-preview svelte-1536g7w")},m(l,a){L(t,l,a),h(l,e,a),h(l,n,a),o=!0},p(l,a){const c={};16384&a[1]&&(c.$$scope={dirty:a,ctx:l}),t.$set(c),(!o||9&a[0]&&!qt(n.src,s="data:"+jt(l[3])+";base64,"+btoa(l[0].originalCode)))&&v(n,"src",s),(!o||4096&a[0]&&i!==(i="Original "+ot(l[12])))&&v(n,"alt",i)},i(l){o||(f(t.$$.fragment,l),o=!0)},o(l){g(t.$$.fragment,l),o=!1},d(l){l&&(m(e),m(n)),M(t,l)}}}function tl(r){let t;return{c(){t=N("Previous version:")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function el(r){let t,e,n,s;const i=[Io,Po,To,So],o=[];function l(a,c){return a[18]?0:a[17]?1:a[16]?2:3}return e=l(r),n=o[e]=i[e](r),{c(){t=b("div"),n.c(),v(t,"class","changes svelte-1536g7w")},m(a,c){h(a,t,c),o[e].m(t,null),s=!0},p(a,c){let u=e;e=l(a),e===u?o[e].p(a,c):(j(),g(o[u],1,1,()=>{o[u]=null}),W(),n=o[e],n?n.p(a,c):(n=o[e]=i[e](a),n.c()),f(n,1),n.m(t,null))},i(a){s||(f(n),s=!0)},o(a){g(n),s=!1},d(a){a&&m(t),o[e].d()}}}function nl(r){let t,e=ot(r[12])+"";return{c(){t=N(e)},m(n,s){h(n,t,s)},p(n,s){4096&s[0]&&e!==(e=ot(n[12])+"")&&rt(t,e)},d(n){n&&m(t)}}}function sl(r){let t,e;return t=new Tt({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$slots:{default:[nl]},$$scope:{ctx:r}}}),t.$on("click",r[28]),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};4096&s[0]|16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function In(r){let t,e,n=se(r[12])+"";return{c(){t=b("span"),e=N(n),v(t,"class","c-directory svelte-1536g7w")},m(s,i){h(s,t,i),R(t,e)},p(s,i){4096&i[0]&&n!==(n=se(s[12])+"")&&rt(e,n)},d(s){s&&m(t)}}}function il(r){let t,e,n,s=r[23]>0&&Un(r),i=r[22]>0&&Vn(r);return{c(){t=b("div"),s&&s.c(),e=I(),i&&i.c(),v(t,"class","changes-indicator svelte-1536g7w")},m(o,l){h(o,t,l),s&&s.m(t,null),R(t,e),i&&i.m(t,null),n=!0},p(o,l){o[23]>0?s?(s.p(o,l),8388608&l[0]&&f(s,1)):(s=Un(o),s.c(),f(s,1),s.m(t,e)):s&&(j(),g(s,1,1,()=>{s=null}),W()),o[22]>0?i?(i.p(o,l),4194304&l[0]&&f(i,1)):(i=Vn(o),i.c(),f(i,1),i.m(t,null)):i&&(j(),g(i,1,1,()=>{i=null}),W())},i(o){n||(f(s),f(i),n=!0)},o(o){g(s),g(i),n=!1},d(o){o&&m(t),s&&s.d(),i&&i.d()}}}function rl(r){let t;return{c(){t=b("span"),t.textContent="New File",v(t,"class","new-file-badge svelte-1536g7w")},m(e,n){h(e,t,n)},p:st,i:st,o:st,d(e){e&&m(t)}}}function Un(r){let t,e,n;return e=new $t({props:{size:1,$$slots:{default:[ol]},$$scope:{ctx:r}}}),{c(){t=b("span"),z(e.$$.fragment),v(t,"class","additions svelte-1536g7w")},m(s,i){h(s,t,i),L(e,t,null),n=!0},p(s,i){const o={};8388608&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function ol(r){let t,e;return{c(){t=N("+"),e=N(r[23])},m(n,s){h(n,t,s),h(n,e,s)},p(n,s){8388608&s[0]&&rt(e,n[23])},d(n){n&&(m(t),m(e))}}}function Vn(r){let t,e,n;return e=new $t({props:{size:1,$$slots:{default:[ll]},$$scope:{ctx:r}}}),{c(){t=b("span"),z(e.$$.fragment),v(t,"class","deletions svelte-1536g7w")},m(s,i){h(s,t,i),L(e,t,null),n=!0},p(s,i){const o={};4194304&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function ll(r){let t,e;return{c(){t=N("-"),e=N(r[22])},m(n,s){h(n,t,s),h(n,e,s)},p(n,s){4194304&s[0]&&rt(e,n[22])},d(n){n&&(m(t),m(e))}}}function al(r){let t;return{c(){t=N("Apply")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function cl(r){let t;return{c(){t=N("Applied")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function ul(r){let t,e,n;return e=new fe({}),{c(){t=b("div"),z(e.$$.fragment),v(t,"class","applied__icon svelte-1536g7w")},m(s,i){h(s,t,i),L(e,t,null),n=!0},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function dl(r){let t,e,n;return e=new Re({props:{iconName:"check"}}),{c(){t=b("div"),z(e.$$.fragment),v(t,"class","applied svelte-1536g7w")},m(s,i){h(s,t,i),L(e,t,null),n=!0},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function pl(r){let t,e,n,s,i;function o(p,C){return p[5]?cl:al}let l=o(r),a=l(r);const c=[dl,ul],u=[];function d(p,C){return p[5]?0:1}return e=d(r),n=u[e]=c[e](r),{c(){a.c(),t=I(),n.c(),s=vt()},m(p,C){a.m(p,C),h(p,t,C),u[e].m(p,C),h(p,s,C),i=!0},p(p,C){l!==(l=o(p))&&(a.d(1),a=l(p),a&&(a.c(),a.m(t.parentNode,t)));let x=e;e=d(p),e!==x&&(j(),g(u[x],1,1,()=>{u[x]=null}),W(),n=u[e],n||(n=u[e]=c[e](p),n.c()),f(n,1),n.m(s.parentNode,s))},i(p){i||(f(n),i=!0)},o(p){g(n),i=!1},d(p){p&&(m(t),m(s)),a.d(p),u[e].d(p)}}}function fl(r){let t,e;return t=new Tt({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[14],$$slots:{default:[pl]},$$scope:{ctx:r}}}),t.$on("click",r[27]),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};16384&s[0]&&(i.disabled=n[14]),32&s[0]|16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function Zn(r){let t,e;return t=new Vt({props:{content:r[11],triggerOn:[re.Hover],delayDurationMs:300,$$slots:{default:[gl]},$$scope:{ctx:r}}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};2048&s[0]&&(i.content=n[11]),16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function $l(r){let t,e;return t=new Qe({}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function gl(r){let t,e;return t=new pe({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[$l]},$$scope:{ctx:r}}}),t.$on("click",r[28]),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};16384&s[1]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function hl(r){let t,e,n,s,i,o,l,a,c,u,d,p,C,x=se(r[12]);e=new ti({}),i=new Vt({props:{content:r[11],triggerOn:[re.Hover],delayDurationMs:300,$$slots:{default:[sl]},$$scope:{ctx:r}}});let $=x&&In(r);const k=[rl,il],_=[];function B(F,w){return F[21]?0:1}a=B(r),c=_[a]=k[a](r),d=new Vt({props:{content:r[13],triggerOn:[re.Hover],delayDurationMs:300,$$slots:{default:[fl]},$$scope:{ctx:r}}});let D=r[5]&&Zn(r);return{c(){t=b("div"),z(e.$$.fragment),n=I(),s=b("div"),z(i.$$.fragment),o=I(),$&&$.c(),l=I(),c.c(),u=I(),z(d.$$.fragment),p=I(),D&&D.c(),v(s,"class","c-path svelte-1536g7w"),v(t,"slot","header"),v(t,"class","header svelte-1536g7w")},m(F,w){h(F,t,w),L(e,t,null),R(t,n),R(t,s),L(i,s,null),R(s,o),$&&$.m(s,null),R(t,l),_[a].m(t,null),R(t,u),L(d,t,null),R(t,p),D&&D.m(t,null),C=!0},p(F,w){const E={};2048&w[0]&&(E.content=F[11]),4096&w[0]|16384&w[1]&&(E.$$scope={dirty:w,ctx:F}),i.$set(E),4096&w[0]&&(x=se(F[12])),x?$?$.p(F,w):($=In(F),$.c(),$.m(s,null)):$&&($.d(1),$=null);let O=a;a=B(F),a===O?_[a].p(F,w):(j(),g(_[O],1,1,()=>{_[O]=null}),W(),c=_[a],c?c.p(F,w):(c=_[a]=k[a](F),c.c()),f(c,1),c.m(t,u));const S={};8192&w[0]&&(S.content=F[13]),16416&w[0]|16384&w[1]&&(S.$$scope={dirty:w,ctx:F}),d.$set(S),F[5]?D?(D.p(F,w),32&w[0]&&f(D,1)):(D=Zn(F),D.c(),f(D,1),D.m(t,null)):D&&(j(),g(D,1,1,()=>{D=null}),W())},i(F){C||(f(e.$$.fragment,F),f(i.$$.fragment,F),f(c),f(d.$$.fragment,F),f(D),C=!0)},o(F){g(e.$$.fragment,F),g(i.$$.fragment,F),g(c),g(d.$$.fragment,F),g(D),C=!1},d(F){F&&m(t),M(e),M(i),$&&$.d(),_[a].d(),M(d),D&&D.d()}}}function ml(r){let t,e,n,s,i;function o(a){r[42](a)}let l={stickyHeader:!0,$$slots:{header:[hl],default:[el]},$$scope:{ctx:r}};return r[2]!==void 0&&(l.collapsed=r[2]),e=new Ks({props:l}),bt.push(()=>ee(e,"collapsed",o)),{c(){t=b("div"),z(e.$$.fragment),v(t,"class","c svelte-1536g7w"),v(t,"id",s=Sn(r[3])),Ft(t,"focused",r[24]===r[3])},m(a,c){h(a,t,c),L(e,t,null),i=!0},p(a,c){const u={};16777211&c[0]|16384&c[1]&&(u.$$scope={dirty:c,ctx:a}),!n&&4&c[0]&&(n=!0,u.collapsed=a[2],ne(()=>n=!1)),e.$set(u),(!i||8&c[0]&&s!==(s=Sn(a[3])))&&v(t,"id",s),(!i||16777224&c[0])&&Ft(t,"focused",a[24]===a[3])},i(a){i||(f(e.$$.fragment,a),i=!0)},o(a){g(e.$$.fragment,a),i=!1},d(a){a&&m(t),M(e)}}}function Dl(r,t,e){let n,s,i,o,l,a,c,u,d,p,C,x,$,k,_,B,D,F,w,E,O,S,T;Rt(r,js,U=>e(40,S=U));let{path:Z}=t,{change:Q}=t,{descriptions:nt=[]}=t,{areDescriptionsVisible:ct=!0}=t,{isExpandedDefault:at}=t,{isCollapsed:lt=!at}=t,{isApplying:J}=t,{hasApplied:ft}=t,{onApplyChanges:mt}=t,{onCodeChange:it}=t,{onOpenFile:At}=t,{isAgentFromDifferentRepo:yt=!1}=t;const Ct=te(qo);Rt(r,Ct,U=>e(24,T=U));const Ot=te(Qi.key);let pt=Q.modifiedCode,Et=w;function kt(){e(11,Et=`Open ${w??"file"}`)}return Gt(()=>{kt()}),r.$$set=U=>{"path"in U&&e(3,Z=U.path),"change"in U&&e(0,Q=U.change),"descriptions"in U&&e(4,nt=U.descriptions),"areDescriptionsVisible"in U&&e(1,ct=U.areDescriptionsVisible),"isExpandedDefault"in U&&e(29,at=U.isExpandedDefault),"isCollapsed"in U&&e(2,lt=U.isCollapsed),"isApplying"in U&&e(30,J=U.isApplying),"hasApplied"in U&&e(5,ft=U.hasApplied),"onApplyChanges"in U&&e(31,mt=U.onApplyChanges),"onCodeChange"in U&&e(32,it=U.onCodeChange),"onOpenFile"in U&&e(33,At=U.onOpenFile),"isAgentFromDifferentRepo"in U&&e(34,yt=U.isAgentFromDifferentRepo)},r.$$.update=()=>{var U;1&r.$$.dirty[0]&&e(6,pt=Q.modifiedCode),1&r.$$.dirty[0]&&e(39,n=Ni(Q.diff)),256&r.$$.dirty[1]&&e(23,s=n.additions),256&r.$$.dirty[1]&&e(22,i=n.deletions),1&r.$$.dirty[0]&&e(21,o=qi(Q)),1&r.$$.dirty[0]&&e(20,l=Si(Q)),8&r.$$.dirty[0]&&e(38,a=si(Z)),8&r.$$.dirty[0]&&e(19,c=jt(Z)),8&r.$$.dirty[0]&&e(37,u=ii(Z)),1&r.$$.dirty[0]&&e(10,d=((U=Q.originalCode)==null?void 0:U.length)||0),64&r.$$.dirty[0]&&e(9,p=(pt==null?void 0:pt.length)||0),1024&r.$$.dirty[0]&&e(36,C=ke(d)),512&r.$$.dirty[0]&&e(35,x=ke(p)),65&r.$$.dirty[0]&&e(8,$=!pt&&!!Q.originalCode),65&r.$$.dirty[0]&&e(7,k=!!pt&&!Q.originalCode),128&r.$$.dirty[1]&&e(18,_=a),192&r.$$.dirty[1]&&e(17,B=!a&&u),384&r.$$.dirty[0]|240&r.$$.dirty[1]&&e(16,D=!a&&!u&&(x||$&&C||k&&x)),512&r.$$.dirty[1]&&e(15,F=ni(S==null?void 0:S.category,S==null?void 0:S.intensity)),8&r.$$.dirty[0]&&e(12,w=ri(Z)),1073741824&r.$$.dirty[0]|8&r.$$.dirty[1]&&e(14,E=J||yt),1073741856&r.$$.dirty[0]|8&r.$$.dirty[1]&&e(13,O=J?"Applying changes...":ft?"Reapply changes to local file":yt?"Cannot apply changes from a different repository locally":"Apply changes to local file")},[Q,ct,lt,Z,nt,ft,pt,k,$,p,d,Et,w,O,E,F,D,B,_,c,l,o,i,s,T,Ct,function(U){e(6,pt=U.detail.modifiedCode),it==null||it(pt)},function(){Ot.reportApplyChangesEvent(),e(0,Q.modifiedCode=pt,Q),it==null||it(pt),mt==null||mt()},async function(){At&&(e(11,Et="Opening file..."),await At()?kt():(e(11,Et="Failed to open file. Does the file exist?"),setTimeout(()=>{kt()},2e3)))},at,J,mt,it,At,yt,x,C,u,a,n,S,function(U){ct=U,e(1,ct)},function(U){lt=U,e(2,lt)}]}class Fl extends tt{constructor(t){super(),et(this,t,Dl,ml,K,{path:3,change:0,descriptions:4,areDescriptionsVisible:1,isExpandedDefault:29,isCollapsed:2,isApplying:30,hasApplied:5,onApplyChanges:31,onCodeChange:32,onOpenFile:33,isAgentFromDifferentRepo:34},null,[-1,-1])}}function Hn(r,t,e){const n=r.slice();return n[1]=t[e],n[3]=e,n}function xl(r,t,e){const n=r.slice();return n[1]=t[e],n}function Cl(r,t,e){const n=r.slice();return n[1]=t[e],n}function wl(r){let t;return{c(){t=b("div"),t.innerHTML='<div class="c-skeleton-diff__file-header svelte-1eiztmz"><div class="c-skeleton-diff__file-info svelte-1eiztmz"><div class="c-skeleton-diff__file-icon svelte-1eiztmz"></div> <div class="c-skeleton-diff__file-path svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__file-actions svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__code-block svelte-1eiztmz"><div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 70%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 85%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 60%;"></span></div></div>',v(t,"class","c-skeleton-diff__changes-item svelte-1eiztmz")},m(e,n){h(e,t,n)},p:st,d(e){e&&m(t)}}}function vl(r){let t,e,n,s,i=dt(Array(2)),o=[];for(let l=0;l<i.length;l+=1)o[l]=wl(Cl(r,i,l));return{c(){t=b("div"),e=b("div"),e.innerHTML='<div class="c-skeleton-diff__content svelte-1eiztmz"><div class="c-skeleton-diff__subtitle svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__icon svelte-1eiztmz"></div>',n=I(),s=b("div");for(let l=0;l<o.length;l+=1)o[l].c();v(e,"class","c-skeleton-diff__header svelte-1eiztmz"),v(s,"class","c-skeleton-diff__changes svelte-1eiztmz"),v(t,"class","c-skeleton-diff__subsection svelte-1eiztmz")},m(l,a){h(l,t,a),R(t,e),R(t,n),R(t,s);for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(s,null)},p:st,d(l){l&&m(t),Mt(o,l)}}}function jn(r){let t,e,n,s,i,o,l=r[3]===0&&function(u){let d;return{c(){d=b("div"),d.innerHTML='<div class="c-skeleton-diff__button svelte-1eiztmz"></div>',v(d,"class","c-skeleton-diff__controls svelte-1eiztmz")},m(p,C){h(p,d,C)},d(p){p&&m(d)}}}(),a=dt(Array(2)),c=[];for(let u=0;u<a.length;u+=1)c[u]=vl(xl(r,a,u));return{c(){t=b("div"),e=b("div"),n=b("div"),n.innerHTML='<div class="c-skeleton-diff__title svelte-1eiztmz"></div> <div class="c-skeleton-diff__description svelte-1eiztmz"><div class="c-skeleton-diff__line svelte-1eiztmz"></div> <div class="c-skeleton-diff__line svelte-1eiztmz" style="width: 85%;"></div></div>',s=I(),l&&l.c(),i=I();for(let u=0;u<c.length;u+=1)c[u].c();o=I(),v(n,"class","c-skeleton-diff__content svelte-1eiztmz"),v(e,"class","c-skeleton-diff__header svelte-1eiztmz"),v(t,"class","c-skeleton-diff__section svelte-1eiztmz")},m(u,d){h(u,t,d),R(t,e),R(e,n),R(e,s),l&&l.m(e,null),R(t,i);for(let p=0;p<c.length;p+=1)c[p]&&c[p].m(t,null);R(t,o)},p(u,d){},d(u){u&&m(t),l&&l.d(),Mt(c,u)}}}function yl(r){let t,e=dt(Array(r[0])),n=[];for(let s=0;s<e.length;s+=1)n[s]=jn(Hn(r,e,s));return{c(){t=b("div");for(let s=0;s<n.length;s+=1)n[s].c();v(t,"class","c-skeleton-diff svelte-1eiztmz")},m(s,i){h(s,t,i);for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(t,null)},p(s,[i]){if(1&i){let o;for(e=dt(Array(s[0])),o=0;o<e.length;o+=1){const l=Hn(s,e,o);n[o]?n[o].p(l,i):(n[o]=jn(l),n[o].c(),n[o].m(t,null))}for(;o<n.length;o+=1)n[o].d(1);n.length=e.length}},i:st,o:st,d(s){s&&m(t),Mt(n,s)}}}function kl(r,t,e){let{count:n=2}=t;return r.$$set=s=>{"count"in s&&e(0,n=s.count)},[n]}class Al extends tt{constructor(t){super(),et(this,t,kl,yl,K,{count:0})}}function Wn(...r){return"/"+r.flatMap(t=>t.split("/")).filter(t=>!!t).join("/")}function Qn(r){return r.startsWith("/")||r.startsWith("#")}function Se(r){let t,e;const n=r[5].default,s=_t(n,r,r[4],null);let i=[{id:r[1]}],o={};for(let l=0;l<i.length;l+=1)o=Qs(o,i[l]);return{c(){t=b(`h${r[0].depth}`),s&&s.c(),ve(`h${r[0].depth}`)(t,o)},m(l,a){h(l,t,a),s&&s.m(t,null),e=!0},p(l,a){s&&s.p&&(!e||16&a)&&Bt(s,n,l,l[4],e?Lt(n,l[4],a,null):zt(l[4]),null),ve(`h${l[0].depth}`)(t,o=Gs(i,[(!e||2&a)&&{id:l[1]}]))},i(l){e||(f(s,l),e=!0)},o(l){g(s,l),e=!1},d(l){l&&m(t),s&&s.d(l)}}}function bl(r){let t,e,n=`h${r[0].depth}`,s=`h${r[0].depth}`&&Se(r);return{c(){s&&s.c(),t=vt()},m(i,o){s&&s.m(i,o),h(i,t,o),e=!0},p(i,[o]){`h${i[0].depth}`?n?K(n,`h${i[0].depth}`)?(s.d(1),s=Se(i),n=`h${i[0].depth}`,s.c(),s.m(t.parentNode,t)):s.p(i,o):(s=Se(i),n=`h${i[0].depth}`,s.c(),s.m(t.parentNode,t)):n&&(s.d(1),s=null,n=`h${i[0].depth}`)},i(i){e||(f(s,i),e=!0)},o(i){g(s,i),e=!1},d(i){i&&m(t),s&&s.d(i)}}}function El(r,t,e){let{$$slots:n={},$$scope:s}=t,{token:i}=t,{options:o}=t,l;return r.$$set=a=>{"token"in a&&e(0,i=a.token),"options"in a&&e(2,o=a.options),"$$scope"in a&&e(4,s=a.$$scope)},r.$$.update=()=>{var a,c;5&r.$$.dirty&&e(1,(a=i.text,c=o.slugger,l=c.slug(a).replace(/--+/g,"-")))},[i,l,o,void 0,s,n]}class _l extends tt{constructor(t){super(),et(this,t,El,bl,K,{token:0,options:2,renderers:3})}get renderers(){return this.$$.ctx[3]}}function Bl(r){let t,e;const n=r[4].default,s=_t(n,r,r[3],null);return{c(){t=b("blockquote"),s&&s.c()},m(i,o){h(i,t,o),s&&s.m(t,null),e=!0},p(i,[o]){s&&s.p&&(!e||8&o)&&Bt(s,n,i,i[3],e?Lt(n,i[3],o,null):zt(i[3]),null)},i(i){e||(f(s,i),e=!0)},o(i){g(s,i),e=!1},d(i){i&&m(t),s&&s.d(i)}}}function zl(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Ll extends tt{constructor(t){super(),et(this,t,zl,Bl,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Gn(r,t,e){const n=r.slice();return n[3]=t[e],n}function Jn(r){let t,e,n=dt(r[0]),s=[];for(let o=0;o<n.length;o+=1)s[o]=Yn(Gn(r,n,o));const i=o=>g(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();t=vt()},m(o,l){for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(o,l);h(o,t,l),e=!0},p(o,l){if(7&l){let a;for(n=dt(o[0]),a=0;a<n.length;a+=1){const c=Gn(o,n,a);s[a]?(s[a].p(c,l),f(s[a],1)):(s[a]=Yn(c),s[a].c(),f(s[a],1),s[a].m(t.parentNode,t))}for(j(),a=n.length;a<s.length;a+=1)i(a);W()}},i(o){if(!e){for(let l=0;l<n.length;l+=1)f(s[l]);e=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)g(s[l]);e=!1},d(o){o&&m(t),Mt(s,o)}}}function Yn(r){let t,e;return t=new gi({props:{token:r[3],renderers:r[1],options:r[2]}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};1&s&&(i.token=n[3]),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function Ml(r){let t,e,n=r[0]&&Jn(r);return{c(){n&&n.c(),t=vt()},m(s,i){n&&n.m(s,i),h(s,t,i),e=!0},p(s,[i]){s[0]?n?(n.p(s,i),1&i&&f(n,1)):(n=Jn(s),n.c(),f(n,1),n.m(t.parentNode,t)):n&&(j(),g(n,1,1,()=>{n=null}),W())},i(s){e||(f(n),e=!0)},o(s){g(n),e=!1},d(s){s&&m(t),n&&n.d(s)}}}function Rl(r,t,e){let{tokens:n}=t,{renderers:s}=t,{options:i}=t;return r.$$set=o=>{"tokens"in o&&e(0,n=o.tokens),"renderers"in o&&e(1,s=o.renderers),"options"in o&&e(2,i=o.options)},[n,s,i]}class Oe extends tt{constructor(t){super(),et(this,t,Rl,Ml,K,{tokens:0,renderers:1,options:2})}}function Xn(r){let t,e,n;var s=r[1][r[0].type];function i(o,l){return{props:{token:o[0],options:o[2],renderers:o[1],$$slots:{default:[ql]},$$scope:{ctx:o}}}}return s&&(t=ln(s,i(r))),{c(){t&&z(t.$$.fragment),e=vt()},m(o,l){t&&L(t,o,l),h(o,e,l),n=!0},p(o,l){if(3&l&&s!==(s=o[1][o[0].type])){if(t){j();const a=t;g(a.$$.fragment,1,0,()=>{M(a,1)}),W()}s?(t=ln(s,i(o)),z(t.$$.fragment),f(t.$$.fragment,1),L(t,e.parentNode,e)):t=null}else if(s){const a={};1&l&&(a.token=o[0]),4&l&&(a.options=o[2]),2&l&&(a.renderers=o[1]),15&l&&(a.$$scope={dirty:l,ctx:o}),t.$set(a)}},i(o){n||(t&&f(t.$$.fragment,o),n=!0)},o(o){t&&g(t.$$.fragment,o),n=!1},d(o){o&&m(e),t&&M(t,o)}}}function Ol(r){let t,e=r[0].raw+"";return{c(){t=N(e)},m(n,s){h(n,t,s)},p(n,s){1&s&&e!==(e=n[0].raw+"")&&rt(t,e)},i:st,o:st,d(n){n&&m(t)}}}function Nl(r){let t,e;return t=new Oe({props:{tokens:r[0].tokens,renderers:r[1],options:r[2]}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};1&s&&(i.tokens=n[0].tokens),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function ql(r){let t,e,n,s;const i=[Nl,Ol],o=[];function l(a,c){return"tokens"in a[0]&&a[0].tokens?0:1}return t=l(r),e=o[t]=i[t](r),{c(){e.c(),n=vt()},m(a,c){o[t].m(a,c),h(a,n,c),s=!0},p(a,c){let u=t;t=l(a),t===u?o[t].p(a,c):(j(),g(o[u],1,1,()=>{o[u]=null}),W(),e=o[t],e?e.p(a,c):(e=o[t]=i[t](a),e.c()),f(e,1),e.m(n.parentNode,n))},i(a){s||(f(e),s=!0)},o(a){g(e),s=!1},d(a){a&&m(n),o[t].d(a)}}}function Sl(r){let t,e,n=r[1][r[0].type]&&Xn(r);return{c(){n&&n.c(),t=vt()},m(s,i){n&&n.m(s,i),h(s,t,i),e=!0},p(s,[i]){s[1][s[0].type]?n?(n.p(s,i),3&i&&f(n,1)):(n=Xn(s),n.c(),f(n,1),n.m(t.parentNode,t)):n&&(j(),g(n,1,1,()=>{n=null}),W())},i(s){e||(f(n),e=!0)},o(s){g(n),e=!1},d(s){s&&m(t),n&&n.d(s)}}}function Tl(r,t,e){let{token:n}=t,{renderers:s}=t,{options:i}=t;return r.$$set=o=>{"token"in o&&e(0,n=o.token),"renderers"in o&&e(1,s=o.renderers),"options"in o&&e(2,i=o.options)},[n,s,i]}class gi extends tt{constructor(t){super(),et(this,t,Tl,Sl,K,{token:0,renderers:1,options:2})}}function Kn(r,t,e){const n=r.slice();return n[4]=t[e],n}function ts(r){let t,e;return t=new gi({props:{token:{...r[4]},options:r[1],renderers:r[2]}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};1&s&&(i.token={...n[4]}),2&s&&(i.options=n[1]),4&s&&(i.renderers=n[2]),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function Te(r){let t,e,n,s=dt(r[0].items),i=[];for(let c=0;c<s.length;c+=1)i[c]=ts(Kn(r,s,c));const o=c=>g(i[c],1,1,()=>{i[c]=null});let l=[{start:e=r[0].start||1}],a={};for(let c=0;c<l.length;c+=1)a=Qs(a,l[c]);return{c(){t=b(r[3]);for(let c=0;c<i.length;c+=1)i[c].c();ve(r[3])(t,a)},m(c,u){h(c,t,u);for(let d=0;d<i.length;d+=1)i[d]&&i[d].m(t,null);n=!0},p(c,u){if(7&u){let d;for(s=dt(c[0].items),d=0;d<s.length;d+=1){const p=Kn(c,s,d);i[d]?(i[d].p(p,u),f(i[d],1)):(i[d]=ts(p),i[d].c(),f(i[d],1),i[d].m(t,null))}for(j(),d=s.length;d<i.length;d+=1)o(d);W()}ve(c[3])(t,a=Gs(l,[(!n||1&u&&e!==(e=c[0].start||1))&&{start:e}]))},i(c){if(!n){for(let u=0;u<s.length;u+=1)f(i[u]);n=!0}},o(c){i=i.filter(Boolean);for(let u=0;u<i.length;u+=1)g(i[u]);n=!1},d(c){c&&m(t),Mt(i,c)}}}function Pl(r){let t,e=r[3],n=r[3]&&Te(r);return{c(){n&&n.c(),t=vt()},m(s,i){n&&n.m(s,i),h(s,t,i)},p(s,[i]){s[3]?e?K(e,s[3])?(n.d(1),n=Te(s),e=s[3],n.c(),n.m(t.parentNode,t)):n.p(s,i):(n=Te(s),e=s[3],n.c(),n.m(t.parentNode,t)):e&&(n.d(1),n=null,e=s[3])},i:st,o(s){g(n,s)},d(s){s&&m(t),n&&n.d(s)}}}function Il(r,t,e){let n,{token:s}=t,{options:i}=t,{renderers:o}=t;return r.$$set=l=>{"token"in l&&e(0,s=l.token),"options"in l&&e(1,i=l.options),"renderers"in l&&e(2,o=l.renderers)},r.$$.update=()=>{1&r.$$.dirty&&e(3,n=s.ordered?"ol":"ul")},[s,i,o,n]}class Ul extends tt{constructor(t){super(),et(this,t,Il,Pl,K,{token:0,options:1,renderers:2})}}function Vl(r){let t,e;const n=r[4].default,s=_t(n,r,r[3],null);return{c(){t=b("li"),s&&s.c()},m(i,o){h(i,t,o),s&&s.m(t,null),e=!0},p(i,[o]){s&&s.p&&(!e||8&o)&&Bt(s,n,i,i[3],e?Lt(n,i[3],o,null):zt(i[3]),null)},i(i){e||(f(s,i),e=!0)},o(i){g(s,i),e=!1},d(i){i&&m(t),s&&s.d(i)}}}function Zl(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Hl extends tt{constructor(t){super(),et(this,t,Zl,Vl,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function jl(r){let t;return{c(){t=b("br")},m(e,n){h(e,t,n)},p:st,i:st,o:st,d(e){e&&m(t)}}}function Wl(r,t,e){return[void 0,void 0,void 0]}class Ql extends tt{constructor(t){super(),et(this,t,Wl,jl,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Gl(r){let t,e,n,s,i=r[0].text+"";return{c(){t=b("pre"),e=b("code"),n=N(i),v(e,"class",s=`lang-${r[0].lang}`)},m(o,l){h(o,t,l),R(t,e),R(e,n)},p(o,[l]){1&l&&i!==(i=o[0].text+"")&&rt(n,i),1&l&&s!==(s=`lang-${o[0].lang}`)&&v(e,"class",s)},i:st,o:st,d(o){o&&m(t)}}}function Jl(r,t,e){let{token:n}=t;return r.$$set=s=>{"token"in s&&e(0,n=s.token)},[n,void 0,void 0]}class Yl extends tt{constructor(t){super(),et(this,t,Jl,Gl,K,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Xl(r){let t,e,n=r[0].raw.slice(1,r[0].raw.length-1)+"";return{c(){t=b("code"),e=N(n)},m(s,i){h(s,t,i),R(t,e)},p(s,[i]){1&i&&n!==(n=s[0].raw.slice(1,s[0].raw.length-1)+"")&&rt(e,n)},i:st,o:st,d(s){s&&m(t)}}}function Kl(r,t,e){let{token:n}=t;return r.$$set=s=>{"token"in s&&e(0,n=s.token)},[n,void 0,void 0]}class ta extends tt{constructor(t){super(),et(this,t,Kl,Xl,K,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function es(r,t,e){const n=r.slice();return n[3]=t[e],n}function ns(r,t,e){const n=r.slice();return n[6]=t[e],n}function ss(r,t,e){const n=r.slice();return n[9]=t[e],n}function is(r){let t,e,n,s;return e=new Oe({props:{tokens:r[9].tokens,options:r[1],renderers:r[2]}}),{c(){t=b("th"),z(e.$$.fragment),n=I(),v(t,"scope","col")},m(i,o){h(i,t,o),L(e,t,null),R(t,n),s=!0},p(i,o){const l={};1&o&&(l.tokens=i[9].tokens),2&o&&(l.options=i[1]),4&o&&(l.renderers=i[2]),e.$set(l)},i(i){s||(f(e.$$.fragment,i),s=!0)},o(i){g(e.$$.fragment,i),s=!1},d(i){i&&m(t),M(e)}}}function rs(r){let t,e,n;return e=new Oe({props:{tokens:r[6].tokens,options:r[1],renderers:r[2]}}),{c(){t=b("td"),z(e.$$.fragment)},m(s,i){h(s,t,i),L(e,t,null),n=!0},p(s,i){const o={};1&i&&(o.tokens=s[6].tokens),2&i&&(o.options=s[1]),4&i&&(o.renderers=s[2]),e.$set(o)},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function os(r){let t,e,n,s=dt(r[3]),i=[];for(let l=0;l<s.length;l+=1)i[l]=rs(ns(r,s,l));const o=l=>g(i[l],1,1,()=>{i[l]=null});return{c(){t=b("tr");for(let l=0;l<i.length;l+=1)i[l].c();e=I()},m(l,a){h(l,t,a);for(let c=0;c<i.length;c+=1)i[c]&&i[c].m(t,null);R(t,e),n=!0},p(l,a){if(7&a){let c;for(s=dt(l[3]),c=0;c<s.length;c+=1){const u=ns(l,s,c);i[c]?(i[c].p(u,a),f(i[c],1)):(i[c]=rs(u),i[c].c(),f(i[c],1),i[c].m(t,e))}for(j(),c=s.length;c<i.length;c+=1)o(c);W()}},i(l){if(!n){for(let a=0;a<s.length;a+=1)f(i[a]);n=!0}},o(l){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)g(i[a]);n=!1},d(l){l&&m(t),Mt(i,l)}}}function ea(r){let t,e,n,s,i,o,l=dt(r[0].header),a=[];for(let C=0;C<l.length;C+=1)a[C]=is(ss(r,l,C));const c=C=>g(a[C],1,1,()=>{a[C]=null});let u=dt(r[0].rows),d=[];for(let C=0;C<u.length;C+=1)d[C]=os(es(r,u,C));const p=C=>g(d[C],1,1,()=>{d[C]=null});return{c(){t=b("table"),e=b("thead"),n=b("tr");for(let C=0;C<a.length;C+=1)a[C].c();s=I(),i=b("tbody");for(let C=0;C<d.length;C+=1)d[C].c()},m(C,x){h(C,t,x),R(t,e),R(e,n);for(let $=0;$<a.length;$+=1)a[$]&&a[$].m(n,null);R(t,s),R(t,i);for(let $=0;$<d.length;$+=1)d[$]&&d[$].m(i,null);o=!0},p(C,[x]){if(7&x){let $;for(l=dt(C[0].header),$=0;$<l.length;$+=1){const k=ss(C,l,$);a[$]?(a[$].p(k,x),f(a[$],1)):(a[$]=is(k),a[$].c(),f(a[$],1),a[$].m(n,null))}for(j(),$=l.length;$<a.length;$+=1)c($);W()}if(7&x){let $;for(u=dt(C[0].rows),$=0;$<u.length;$+=1){const k=es(C,u,$);d[$]?(d[$].p(k,x),f(d[$],1)):(d[$]=os(k),d[$].c(),f(d[$],1),d[$].m(i,null))}for(j(),$=u.length;$<d.length;$+=1)p($);W()}},i(C){if(!o){for(let x=0;x<l.length;x+=1)f(a[x]);for(let x=0;x<u.length;x+=1)f(d[x]);o=!0}},o(C){a=a.filter(Boolean);for(let x=0;x<a.length;x+=1)g(a[x]);d=d.filter(Boolean);for(let x=0;x<d.length;x+=1)g(d[x]);o=!1},d(C){C&&m(t),Mt(a,C),Mt(d,C)}}}function na(r,t,e){let{token:n}=t,{options:s}=t,{renderers:i}=t;return r.$$set=o=>{"token"in o&&e(0,n=o.token),"options"in o&&e(1,s=o.options),"renderers"in o&&e(2,i=o.renderers)},[n,s,i]}class sa extends tt{constructor(t){super(),et(this,t,na,ea,K,{token:0,options:1,renderers:2})}}function ia(r){let t,e,n=r[0].text+"";return{c(){t=new _i(!1),e=vt(),t.a=e},m(s,i){t.m(n,s,i),h(s,e,i)},p(s,[i]){1&i&&n!==(n=s[0].text+"")&&t.p(n)},i:st,o:st,d(s){s&&(m(e),t.d())}}}function ra(r,t,e){let{token:n}=t;return r.$$set=s=>{"token"in s&&e(0,n=s.token)},[n,void 0,void 0]}class oa extends tt{constructor(t){super(),et(this,t,ra,ia,K,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function la(r){let t,e;const n=r[4].default,s=_t(n,r,r[3],null);return{c(){t=b("p"),s&&s.c()},m(i,o){h(i,t,o),s&&s.m(t,null),e=!0},p(i,[o]){s&&s.p&&(!e||8&o)&&Bt(s,n,i,i[3],e?Lt(n,i[3],o,null):zt(i[3]),null)},i(i){e||(f(s,i),e=!0)},o(i){g(s,i),e=!1},d(i){i&&m(t),s&&s.d(i)}}}function aa(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}let ca=class extends tt{constructor(r){super(),et(this,r,aa,la,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}};function ua(r){let t,e,n,s;const i=r[4].default,o=_t(i,r,r[3],null);return{c(){t=b("a"),o&&o.c(),v(t,"href",e=Qn(r[0].href)?Wn(r[1].baseUrl,r[0].href):r[0].href),v(t,"title",n=r[0].title)},m(l,a){h(l,t,a),o&&o.m(t,null),s=!0},p(l,[a]){o&&o.p&&(!s||8&a)&&Bt(o,i,l,l[3],s?Lt(i,l[3],a,null):zt(l[3]),null),(!s||3&a&&e!==(e=Qn(l[0].href)?Wn(l[1].baseUrl,l[0].href):l[0].href))&&v(t,"href",e),(!s||1&a&&n!==(n=l[0].title))&&v(t,"title",n)},i(l){s||(f(o,l),s=!0)},o(l){g(o,l),s=!1},d(l){l&&m(t),o&&o.d(l)}}}function da(r,t,e){let{$$slots:n={},$$scope:s}=t,{token:i}=t,{options:o}=t;return r.$$set=l=>{"token"in l&&e(0,i=l.token),"options"in l&&e(1,o=l.options),"$$scope"in l&&e(3,s=l.$$scope)},[i,o,void 0,s,n]}class pa extends tt{constructor(t){super(),et(this,t,da,ua,K,{token:0,options:1,renderers:2})}get renderers(){return this.$$.ctx[2]}}function fa(r){let t;const e=r[4].default,n=_t(e,r,r[3],null);return{c(){n&&n.c()},m(s,i){n&&n.m(s,i),t=!0},p(s,[i]){n&&n.p&&(!t||8&i)&&Bt(n,e,s,s[3],t?Lt(e,s[3],i,null):zt(s[3]),null)},i(s){t||(f(n,s),t=!0)},o(s){g(n,s),t=!1},d(s){n&&n.d(s)}}}function $a(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class ga extends tt{constructor(t){super(),et(this,t,$a,fa,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ha(r){let t,e;const n=r[4].default,s=_t(n,r,r[3],null);return{c(){t=b("dfn"),s&&s.c()},m(i,o){h(i,t,o),s&&s.m(t,null),e=!0},p(i,[o]){s&&s.p&&(!e||8&o)&&Bt(s,n,i,i[3],e?Lt(n,i[3],o,null):zt(i[3]),null)},i(i){e||(f(s,i),e=!0)},o(i){g(s,i),e=!1},d(i){i&&m(t),s&&s.d(i)}}}function ma(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Da extends tt{constructor(t){super(),et(this,t,ma,ha,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Fa(r){let t,e;const n=r[4].default,s=_t(n,r,r[3],null);return{c(){t=b("del"),s&&s.c()},m(i,o){h(i,t,o),s&&s.m(t,null),e=!0},p(i,[o]){s&&s.p&&(!e||8&o)&&Bt(s,n,i,i[3],e?Lt(n,i[3],o,null):zt(i[3]),null)},i(i){e||(f(s,i),e=!0)},o(i){g(s,i),e=!1},d(i){i&&m(t),s&&s.d(i)}}}function xa(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Ca extends tt{constructor(t){super(),et(this,t,xa,Fa,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function wa(r){let t,e;const n=r[4].default,s=_t(n,r,r[3],null);return{c(){t=b("em"),s&&s.c()},m(i,o){h(i,t,o),s&&s.m(t,null),e=!0},p(i,[o]){s&&s.p&&(!e||8&o)&&Bt(s,n,i,i[3],e?Lt(n,i[3],o,null):zt(i[3]),null)},i(i){e||(f(s,i),e=!0)},o(i){g(s,i),e=!1},d(i){i&&m(t),s&&s.d(i)}}}function va(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class ya extends tt{constructor(t){super(),et(this,t,va,wa,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ka(r){let t;return{c(){t=b("hr")},m(e,n){h(e,t,n)},p:st,i:st,o:st,d(e){e&&m(t)}}}function Aa(r,t,e){return[void 0,void 0,void 0]}class ba extends tt{constructor(t){super(),et(this,t,Aa,ka,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Ea(r){let t,e;const n=r[4].default,s=_t(n,r,r[3],null);return{c(){t=b("strong"),s&&s.c()},m(i,o){h(i,t,o),s&&s.m(t,null),e=!0},p(i,[o]){s&&s.p&&(!e||8&o)&&Bt(s,n,i,i[3],e?Lt(n,i[3],o,null):zt(i[3]),null)},i(i){e||(f(s,i),e=!0)},o(i){g(s,i),e=!1},d(i){i&&m(t),s&&s.d(i)}}}function _a(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Ba extends tt{constructor(t){super(),et(this,t,_a,Ea,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function za(r){let t,e,n,s;return{c(){t=b("img"),qt(t.src,e=r[0].href)||v(t,"src",e),v(t,"title",n=r[0].title),v(t,"alt",s=r[0].text),v(t,"class","markdown-image svelte-z38cge")},m(i,o){h(i,t,o)},p(i,[o]){1&o&&!qt(t.src,e=i[0].href)&&v(t,"src",e),1&o&&n!==(n=i[0].title)&&v(t,"title",n),1&o&&s!==(s=i[0].text)&&v(t,"alt",s)},i:st,o:st,d(i){i&&m(t)}}}function La(r,t,e){let{token:n}=t;return r.$$set=s=>{"token"in s&&e(0,n=s.token)},[n,void 0,void 0]}class Ma extends tt{constructor(t){super(),et(this,t,La,za,K,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Ra(r){let t;const e=r[4].default,n=_t(e,r,r[3],null);return{c(){n&&n.c()},m(s,i){n&&n.m(s,i),t=!0},p(s,[i]){n&&n.p&&(!t||8&i)&&Bt(n,e,s,s[3],t?Lt(e,s[3],i,null):zt(s[3]),null)},i(s){t||(f(n,s),t=!0)},o(s){g(n,s),t=!1},d(s){n&&n.d(s)}}}function Oa(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class ls extends tt{constructor(t){super(),et(this,t,Oa,Ra,K,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Na(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let ae={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function as(r){ae=r}const hi=/[&<>"']/,qa=new RegExp(hi.source,"g"),mi=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,Sa=new RegExp(mi.source,"g"),Ta={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},cs=r=>Ta[r];function St(r,t){if(t){if(hi.test(r))return r.replace(qa,cs)}else if(mi.test(r))return r.replace(Sa,cs);return r}const Pa=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function Ia(r){return r.replace(Pa,(t,e)=>(e=e.toLowerCase())==="colon"?":":e.charAt(0)==="#"?e.charAt(1)==="x"?String.fromCharCode(parseInt(e.substring(2),16)):String.fromCharCode(+e.substring(1)):"")}const Ua=/(^|[^\[])\^/g;function xt(r,t){let e=typeof r=="string"?r:r.source;t=t||"";const n={replace:(s,i)=>{let o=typeof i=="string"?i:i.source;return o=o.replace(Ua,"$1"),e=e.replace(s,o),n},getRegex:()=>new RegExp(e,t)};return n}function us(r){try{r=encodeURI(r).replace(/%25/g,"%")}catch{return null}return r}const ue={exec:()=>null};function ds(r,t){const e=r.replace(/\|/g,(s,i,o)=>{let l=!1,a=i;for(;--a>=0&&o[a]==="\\";)l=!l;return l?"|":" |"}).split(/ \|/);let n=0;if(e[0].trim()||e.shift(),e.length>0&&!e[e.length-1].trim()&&e.pop(),t)if(e.length>t)e.splice(t);else for(;e.length<t;)e.push("");for(;n<e.length;n++)e[n]=e[n].trim().replace(/\\\|/g,"|");return e}function xe(r,t,e){const n=r.length;if(n===0)return"";let s=0;for(;s<n;){const i=r.charAt(n-s-1);if(i!==t||e){if(i===t||!e)break;s++}else s++}return r.slice(0,n-s)}function ps(r,t,e,n){const s=t.href,i=t.title?St(t.title):null,o=r[1].replace(/\\([\[\]])/g,"$1");if(r[0].charAt(0)!=="!"){n.state.inLink=!0;const l={type:"link",raw:e,href:s,title:i,text:o,tokens:n.inlineTokens(o)};return n.state.inLink=!1,l}return{type:"image",raw:e,href:s,title:i,text:St(o)}}class Ee{constructor(t){gt(this,"options");gt(this,"rules");gt(this,"lexer");this.options=t||ae}space(t){const e=this.rules.block.newline.exec(t);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(t){const e=this.rules.block.code.exec(t);if(e){const n=e[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?n:xe(n,`
`)}}}fences(t){const e=this.rules.block.fences.exec(t);if(e){const n=e[0],s=function(i,o){const l=i.match(/^(\s+)(?:```)/);if(l===null)return o;const a=l[1];return o.split(`
`).map(c=>{const u=c.match(/^\s+/);if(u===null)return c;const[d]=u;return d.length>=a.length?c.slice(a.length):c}).join(`
`)}(n,e[3]||"");return{type:"code",raw:n,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:s}}}heading(t){const e=this.rules.block.heading.exec(t);if(e){let n=e[2].trim();if(/#$/.test(n)){const s=xe(n,"#");this.options.pedantic?n=s.trim():s&&!/ $/.test(s)||(n=s.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(t){const e=this.rules.block.hr.exec(t);if(e)return{type:"hr",raw:e[0]}}blockquote(t){const e=this.rules.block.blockquote.exec(t);if(e){const n=xe(e[0].replace(/^ *>[ \t]?/gm,""),`
`),s=this.lexer.state.top;this.lexer.state.top=!0;const i=this.lexer.blockTokens(n);return this.lexer.state.top=s,{type:"blockquote",raw:e[0],tokens:i,text:n}}}list(t){let e=this.rules.block.list.exec(t);if(e){let n=e[1].trim();const s=n.length>1,i={type:"list",raw:"",ordered:s,start:s?+n.slice(0,-1):"",loose:!1,items:[]};n=s?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=s?n:"[*+-]");const o=new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`);let l="",a="",c=!1;for(;t;){let u=!1;if(!(e=o.exec(t))||this.rules.block.hr.test(t))break;l=e[0],t=t.substring(l.length);let d=e[2].split(`
`,1)[0].replace(/^\t+/,_=>" ".repeat(3*_.length)),p=t.split(`
`,1)[0],C=0;this.options.pedantic?(C=2,a=d.trimStart()):(C=e[2].search(/[^ ]/),C=C>4?1:C,a=d.slice(C),C+=e[1].length);let x=!1;if(!d&&/^ *$/.test(p)&&(l+=p+`
`,t=t.substring(p.length+1),u=!0),!u){const _=new RegExp(`^ {0,${Math.min(3,C-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),B=new RegExp(`^ {0,${Math.min(3,C-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),D=new RegExp(`^ {0,${Math.min(3,C-1)}}(?:\`\`\`|~~~)`),F=new RegExp(`^ {0,${Math.min(3,C-1)}}#`);for(;t;){const w=t.split(`
`,1)[0];if(p=w,this.options.pedantic&&(p=p.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),D.test(p)||F.test(p)||_.test(p)||B.test(t))break;if(p.search(/[^ ]/)>=C||!p.trim())a+=`
`+p.slice(C);else{if(x||d.search(/[^ ]/)>=4||D.test(d)||F.test(d)||B.test(d))break;a+=`
`+p}x||p.trim()||(x=!0),l+=w+`
`,t=t.substring(w.length+1),d=p.slice(C)}}i.loose||(c?i.loose=!0:/\n *\n *$/.test(l)&&(c=!0));let $,k=null;this.options.gfm&&(k=/^\[[ xX]\] /.exec(a),k&&($=k[0]!=="[ ] ",a=a.replace(/^\[[ xX]\] +/,""))),i.items.push({type:"list_item",raw:l,task:!!k,checked:$,loose:!1,text:a,tokens:[]}),i.raw+=l}i.items[i.items.length-1].raw=l.trimEnd(),i.items[i.items.length-1].text=a.trimEnd(),i.raw=i.raw.trimEnd();for(let u=0;u<i.items.length;u++)if(this.lexer.state.top=!1,i.items[u].tokens=this.lexer.blockTokens(i.items[u].text,[]),!i.loose){const d=i.items[u].tokens.filter(C=>C.type==="space"),p=d.length>0&&d.some(C=>/\n.*\n/.test(C.raw));i.loose=p}if(i.loose)for(let u=0;u<i.items.length;u++)i.items[u].loose=!0;return i}}html(t){const e=this.rules.block.html.exec(t);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(t){const e=this.rules.block.def.exec(t);if(e){const n=e[1].toLowerCase().replace(/\s+/g," "),s=e[2]?e[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",i=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:n,raw:e[0],href:s,title:i}}}table(t){const e=this.rules.block.table.exec(t);if(!e||!/[:|]/.test(e[2]))return;const n=ds(e[1]),s=e[2].replace(/^\||\| *$/g,"").split("|"),i=e[3]&&e[3].trim()?e[3].replace(/\n[ \t]*$/,"").split(`
`):[],o={type:"table",raw:e[0],header:[],align:[],rows:[]};if(n.length===s.length){for(const l of s)/^ *-+: *$/.test(l)?o.align.push("right"):/^ *:-+: *$/.test(l)?o.align.push("center"):/^ *:-+ *$/.test(l)?o.align.push("left"):o.align.push(null);for(const l of n)o.header.push({text:l,tokens:this.lexer.inline(l)});for(const l of i)o.rows.push(ds(l,o.header.length).map(a=>({text:a,tokens:this.lexer.inline(a)})));return o}}lheading(t){const e=this.rules.block.lheading.exec(t);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(t){const e=this.rules.block.paragraph.exec(t);if(e){const n=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:n,tokens:this.lexer.inline(n)}}}text(t){const e=this.rules.block.text.exec(t);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(t){const e=this.rules.inline.escape.exec(t);if(e)return{type:"escape",raw:e[0],text:St(e[1])}}tag(t){const e=this.rules.inline.tag.exec(t);if(e)return!this.lexer.state.inLink&&/^<a /i.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(t){const e=this.rules.inline.link.exec(t);if(e){const n=e[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;const o=xe(n.slice(0,-1),"\\");if((n.length-o.length)%2==0)return}else{const o=function(l,a){if(l.indexOf(a[1])===-1)return-1;let c=0;for(let u=0;u<l.length;u++)if(l[u]==="\\")u++;else if(l[u]===a[0])c++;else if(l[u]===a[1]&&(c--,c<0))return u;return-1}(e[2],"()");if(o>-1){const l=(e[0].indexOf("!")===0?5:4)+e[1].length+o;e[2]=e[2].substring(0,o),e[0]=e[0].substring(0,l).trim(),e[3]=""}}let s=e[2],i="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(s);o&&(s=o[1],i=o[3])}else i=e[3]?e[3].slice(1,-1):"";return s=s.trim(),/^</.test(s)&&(s=this.options.pedantic&&!/>$/.test(n)?s.slice(1):s.slice(1,-1)),ps(e,{href:s&&s.replace(this.rules.inline.anyPunctuation,"$1"),title:i&&i.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer)}}reflink(t,e){let n;if((n=this.rules.inline.reflink.exec(t))||(n=this.rules.inline.nolink.exec(t))){const s=e[(n[2]||n[1]).replace(/\s+/g," ").toLowerCase()];if(!s){const i=n[0].charAt(0);return{type:"text",raw:i,text:i}}return ps(n,s,n[0],this.lexer)}}emStrong(t,e,n=""){let s=this.rules.inline.emStrongLDelim.exec(t);if(s&&!(s[3]&&n.match(/[\p{L}\p{N}]/u))&&(!(s[1]||s[2])||!n||this.rules.inline.punctuation.exec(n))){const i=[...s[0]].length-1;let o,l,a=i,c=0;const u=s[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(u.lastIndex=0,e=e.slice(-1*t.length+i);(s=u.exec(e))!=null;){if(o=s[1]||s[2]||s[3]||s[4]||s[5]||s[6],!o)continue;if(l=[...o].length,s[3]||s[4]){a+=l;continue}if((s[5]||s[6])&&i%3&&!((i+l)%3)){c+=l;continue}if(a-=l,a>0)continue;l=Math.min(l,l+a+c);const d=[...s[0]][0].length,p=t.slice(0,i+s.index+d+l);if(Math.min(i,l)%2){const x=p.slice(1,-1);return{type:"em",raw:p,text:x,tokens:this.lexer.inlineTokens(x)}}const C=p.slice(2,-2);return{type:"strong",raw:p,text:C,tokens:this.lexer.inlineTokens(C)}}}}codespan(t){const e=this.rules.inline.code.exec(t);if(e){let n=e[2].replace(/\n/g," ");const s=/[^ ]/.test(n),i=/^ /.test(n)&&/ $/.test(n);return s&&i&&(n=n.substring(1,n.length-1)),n=St(n,!0),{type:"codespan",raw:e[0],text:n}}}br(t){const e=this.rules.inline.br.exec(t);if(e)return{type:"br",raw:e[0]}}del(t){const e=this.rules.inline.del.exec(t);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(t){const e=this.rules.inline.autolink.exec(t);if(e){let n,s;return e[2]==="@"?(n=St(e[1]),s="mailto:"+n):(n=St(e[1]),s=n),{type:"link",raw:e[0],text:n,href:s,tokens:[{type:"text",raw:n,text:n}]}}}url(t){var n;let e;if(e=this.rules.inline.url.exec(t)){let s,i;if(e[2]==="@")s=St(e[0]),i="mailto:"+s;else{let o;do o=e[0],e[0]=((n=this.rules.inline._backpedal.exec(e[0]))==null?void 0:n[0])??"";while(o!==e[0]);s=St(e[0]),i=e[1]==="www."?"http://"+e[0]:e[0]}return{type:"link",raw:e[0],text:s,href:i,tokens:[{type:"text",raw:s,text:s}]}}}inlineText(t){const e=this.rules.inline.text.exec(t);if(e){let n;return n=this.lexer.state.inRawBlock?e[0]:St(e[0]),{type:"text",raw:e[0],text:n}}}}const ge=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Di=/(?:[*+-]|\d{1,9}[.)])/,Fi=xt(/^(?!bull )((?:.|\n(?!\s*?\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,Di).getRegex(),Ye=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Xe=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Va=xt(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",Xe).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Za=xt(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Di).getRegex(),Ne="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Ke=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,Ha=xt("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",Ke).replace("tag",Ne).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),fs=xt(Ye).replace("hr",ge).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ne).getRegex(),tn={blockquote:xt(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",fs).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:Va,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:ge,html:Ha,lheading:Fi,list:Za,newline:/^(?: *(?:\n|$))+/,paragraph:fs,table:ue,text:/^[^\n]+/},$s=xt("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",ge).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ne).getRegex(),ja={...tn,table:$s,paragraph:xt(Ye).replace("hr",ge).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",$s).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ne).getRegex()},Wa={...tn,html:xt(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Ke).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:ue,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:xt(Ye).replace("hr",ge).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Fi).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},xi=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,Ci=/^( {2,}|\\)\n(?!\s*$)/,he="\\p{P}$+<=>`^|~",Qa=xt(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,he).getRegex(),Ga=xt(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,he).getRegex(),Ja=xt("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,he).getRegex(),Ya=xt("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,he).getRegex(),Xa=xt(/\\([punct])/,"gu").replace(/punct/g,he).getRegex(),Ka=xt(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),tc=xt(Ke).replace("(?:-->|$)","-->").getRegex(),ec=xt("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",tc).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),_e=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,nc=xt(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",_e).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),gs=xt(/^!?\[(label)\]\[(ref)\]/).replace("label",_e).replace("ref",Xe).getRegex(),hs=xt(/^!?\[(ref)\](?:\[\])?/).replace("ref",Xe).getRegex(),en={_backpedal:ue,anyPunctuation:Xa,autolink:Ka,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:Ci,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:ue,emStrongLDelim:Ga,emStrongRDelimAst:Ja,emStrongRDelimUnd:Ya,escape:xi,link:nc,nolink:hs,punctuation:Qa,reflink:gs,reflinkSearch:xt("reflink|nolink(?!\\()","g").replace("reflink",gs).replace("nolink",hs).getRegex(),tag:ec,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:ue},sc={...en,link:xt(/^!?\[(label)\]\((.*?)\)/).replace("label",_e).getRegex(),reflink:xt(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",_e).getRegex()},He={...en,escape:xt(xi).replace("])","~|])").getRegex(),url:xt(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},ic={...He,br:xt(Ci).replace("{2,}","*").getRegex(),text:xt(He.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Ce={normal:tn,gfm:ja,pedantic:Wa},ce={normal:en,gfm:He,breaks:ic,pedantic:sc};class Ut{constructor(t){gt(this,"tokens");gt(this,"options");gt(this,"state");gt(this,"tokenizer");gt(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||ae,this.options.tokenizer=this.options.tokenizer||new Ee,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const e={block:Ce.normal,inline:ce.normal};this.options.pedantic?(e.block=Ce.pedantic,e.inline=ce.pedantic):this.options.gfm&&(e.block=Ce.gfm,this.options.breaks?e.inline=ce.breaks:e.inline=ce.gfm),this.tokenizer.rules=e}static get rules(){return{block:Ce,inline:ce}}static lex(t,e){return new Ut(e).lex(t)}static lexInline(t,e){return new Ut(e).inlineTokens(t)}lex(t){t=t.replace(/\r\n|\r/g,`
`),this.blockTokens(t,this.tokens);for(let e=0;e<this.inlineQueue.length;e++){const n=this.inlineQueue[e];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,e=[]){let n,s,i,o;for(t=this.options.pedantic?t.replace(/\t/g,"    ").replace(/^ +$/gm,""):t.replace(/^( *)(\t+)/gm,(l,a,c)=>a+"    ".repeat(c.length));t;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(l=>!!(n=l.call({lexer:this},t,e))&&(t=t.substring(n.raw.length),e.push(n),!0))))if(n=this.tokenizer.space(t))t=t.substring(n.raw.length),n.raw.length===1&&e.length>0?e[e.length-1].raw+=`
`:e.push(n);else if(n=this.tokenizer.code(t))t=t.substring(n.raw.length),s=e[e.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?e.push(n):(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(n=this.tokenizer.fences(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.heading(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.hr(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.blockquote(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.list(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.html(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.def(t))t=t.substring(n.raw.length),s=e[e.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title}):(s.raw+=`
`+n.raw,s.text+=`
`+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(n=this.tokenizer.table(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.lheading(t))t=t.substring(n.raw.length),e.push(n);else{if(i=t,this.options.extensions&&this.options.extensions.startBlock){let l=1/0;const a=t.slice(1);let c;this.options.extensions.startBlock.forEach(u=>{c=u.call({lexer:this},a),typeof c=="number"&&c>=0&&(l=Math.min(l,c))}),l<1/0&&l>=0&&(i=t.substring(0,l+1))}if(this.state.top&&(n=this.tokenizer.paragraph(i)))s=e[e.length-1],o&&s.type==="paragraph"?(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):e.push(n),o=i.length!==t.length,t=t.substring(n.raw.length);else if(n=this.tokenizer.text(t))t=t.substring(n.raw.length),s=e[e.length-1],s&&s.type==="text"?(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):e.push(n);else if(t){const l="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(l);break}throw new Error(l)}}return this.state.top=!0,e}inline(t,e=[]){return this.inlineQueue.push({src:t,tokens:e}),e}inlineTokens(t,e=[]){let n,s,i,o,l,a,c=t;if(this.tokens.links){const u=Object.keys(this.tokens.links);if(u.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(c))!=null;)u.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(c=c.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(c))!=null;)c=c.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(c))!=null;)c=c.slice(0,o.index)+"++"+c.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;t;)if(l||(a=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(u=>!!(n=u.call({lexer:this},t,e))&&(t=t.substring(n.raw.length),e.push(n),!0))))if(n=this.tokenizer.escape(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.tag(t))t=t.substring(n.raw.length),s=e[e.length-1],s&&n.type==="text"&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):e.push(n);else if(n=this.tokenizer.link(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.reflink(t,this.tokens.links))t=t.substring(n.raw.length),s=e[e.length-1],s&&n.type==="text"&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):e.push(n);else if(n=this.tokenizer.emStrong(t,c,a))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.codespan(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.br(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.del(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.autolink(t))t=t.substring(n.raw.length),e.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(t))){if(i=t,this.options.extensions&&this.options.extensions.startInline){let u=1/0;const d=t.slice(1);let p;this.options.extensions.startInline.forEach(C=>{p=C.call({lexer:this},d),typeof p=="number"&&p>=0&&(u=Math.min(u,p))}),u<1/0&&u>=0&&(i=t.substring(0,u+1))}if(n=this.tokenizer.inlineText(i))t=t.substring(n.raw.length),n.raw.slice(-1)!=="_"&&(a=n.raw.slice(-1)),l=!0,s=e[e.length-1],s&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):e.push(n);else if(t){const u="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(u);break}throw new Error(u)}}else t=t.substring(n.raw.length),e.push(n);return e}}class Be{constructor(t){gt(this,"options");this.options=t||ae}code(t,e,n){var i;const s=(i=(e||"").match(/^\S*/))==null?void 0:i[0];return t=t.replace(/\n$/,"")+`
`,s?'<pre><code class="language-'+St(s)+'">'+(n?t:St(t,!0))+`</code></pre>
`:"<pre><code>"+(n?t:St(t,!0))+`</code></pre>
`}blockquote(t){return`<blockquote>
${t}</blockquote>
`}html(t,e){return t}heading(t,e,n){return`<h${e}>${t}</h${e}>
`}hr(){return`<hr>
`}list(t,e,n){const s=e?"ol":"ul";return"<"+s+(e&&n!==1?' start="'+n+'"':"")+`>
`+t+"</"+s+`>
`}listitem(t,e,n){return`<li>${t}</li>
`}checkbox(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(t){return`<p>${t}</p>
`}table(t,e){return e&&(e=`<tbody>${e}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+e+`</table>
`}tablerow(t){return`<tr>
${t}</tr>
`}tablecell(t,e){const n=e.header?"th":"td";return(e.align?`<${n} align="${e.align}">`:`<${n}>`)+t+`</${n}>
`}strong(t){return`<strong>${t}</strong>`}em(t){return`<em>${t}</em>`}codespan(t){return`<code>${t}</code>`}br(){return"<br>"}del(t){return`<del>${t}</del>`}link(t,e,n){const s=us(t);if(s===null)return n;let i='<a href="'+(t=s)+'"';return e&&(i+=' title="'+e+'"'),i+=">"+n+"</a>",i}image(t,e,n){const s=us(t);if(s===null)return n;let i=`<img src="${t=s}" alt="${n}"`;return e&&(i+=` title="${e}"`),i+=">",i}text(t){return t}}class nn{strong(t){return t}em(t){return t}codespan(t){return t}del(t){return t}html(t){return t}text(t){return t}link(t,e,n){return""+n}image(t,e,n){return""+n}br(){return""}}class Ht{constructor(t){gt(this,"options");gt(this,"renderer");gt(this,"textRenderer");this.options=t||ae,this.options.renderer=this.options.renderer||new Be,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new nn}static parse(t,e){return new Ht(e).parse(t)}static parseInline(t,e){return new Ht(e).parseInline(t)}parse(t,e=!0){let n="";for(let s=0;s<t.length;s++){const i=t[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=i,l=this.options.extensions.renderers[o.type].call({parser:this},o);if(l!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){n+=l||"";continue}}switch(i.type){case"space":continue;case"hr":n+=this.renderer.hr();continue;case"heading":{const o=i;n+=this.renderer.heading(this.parseInline(o.tokens),o.depth,Ia(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=i;n+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=i;let l="",a="";for(let u=0;u<o.header.length;u++)a+=this.renderer.tablecell(this.parseInline(o.header[u].tokens),{header:!0,align:o.align[u]});l+=this.renderer.tablerow(a);let c="";for(let u=0;u<o.rows.length;u++){const d=o.rows[u];a="";for(let p=0;p<d.length;p++)a+=this.renderer.tablecell(this.parseInline(d[p].tokens),{header:!1,align:o.align[p]});c+=this.renderer.tablerow(a)}n+=this.renderer.table(l,c);continue}case"blockquote":{const o=i,l=this.parse(o.tokens);n+=this.renderer.blockquote(l);continue}case"list":{const o=i,l=o.ordered,a=o.start,c=o.loose;let u="";for(let d=0;d<o.items.length;d++){const p=o.items[d],C=p.checked,x=p.task;let $="";if(p.task){const k=this.renderer.checkbox(!!C);c?p.tokens.length>0&&p.tokens[0].type==="paragraph"?(p.tokens[0].text=k+" "+p.tokens[0].text,p.tokens[0].tokens&&p.tokens[0].tokens.length>0&&p.tokens[0].tokens[0].type==="text"&&(p.tokens[0].tokens[0].text=k+" "+p.tokens[0].tokens[0].text)):p.tokens.unshift({type:"text",text:k+" "}):$+=k+" "}$+=this.parse(p.tokens,c),u+=this.renderer.listitem($,x,!!C)}n+=this.renderer.list(u,l,a);continue}case"html":{const o=i;n+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=i;n+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=i,l=o.tokens?this.parseInline(o.tokens):o.text;for(;s+1<t.length&&t[s+1].type==="text";)o=t[++s],l+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);n+=e?this.renderer.paragraph(l):l;continue}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}parseInline(t,e){e=e||this.renderer;let n="";for(let s=0;s<t.length;s++){const i=t[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=this.options.extensions.renderers[i.type].call({parser:this},i);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){n+=o||"";continue}}switch(i.type){case"escape":{const o=i;n+=e.text(o.text);break}case"html":{const o=i;n+=e.html(o.text);break}case"link":{const o=i;n+=e.link(o.href,o.title,this.parseInline(o.tokens,e));break}case"image":{const o=i;n+=e.image(o.href,o.title,o.text);break}case"strong":{const o=i;n+=e.strong(this.parseInline(o.tokens,e));break}case"em":{const o=i;n+=e.em(this.parseInline(o.tokens,e));break}case"codespan":{const o=i;n+=e.codespan(o.text);break}case"br":n+=e.br();break;case"del":{const o=i;n+=e.del(this.parseInline(o.tokens,e));break}case"text":{const o=i;n+=e.text(o.text);break}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}}class de{constructor(t){gt(this,"options");this.options=t||ae}preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}}gt(de,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var le,je,wi,Vs;const ie=new(Vs=class{constructor(...r){rn(this,le);gt(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});gt(this,"options",this.setOptions);gt(this,"parse",De(this,le,je).call(this,Ut.lex,Ht.parse));gt(this,"parseInline",De(this,le,je).call(this,Ut.lexInline,Ht.parseInline));gt(this,"Parser",Ht);gt(this,"Renderer",Be);gt(this,"TextRenderer",nn);gt(this,"Lexer",Ut);gt(this,"Tokenizer",Ee);gt(this,"Hooks",de);this.use(...r)}walkTokens(r,t){var n,s;let e=[];for(const i of r)switch(e=e.concat(t.call(this,i)),i.type){case"table":{const o=i;for(const l of o.header)e=e.concat(this.walkTokens(l.tokens,t));for(const l of o.rows)for(const a of l)e=e.concat(this.walkTokens(a.tokens,t));break}case"list":{const o=i;e=e.concat(this.walkTokens(o.items,t));break}default:{const o=i;(s=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&s[o.type]?this.defaults.extensions.childTokens[o.type].forEach(l=>{const a=o[l].flat(1/0);e=e.concat(this.walkTokens(a,t))}):o.tokens&&(e=e.concat(this.walkTokens(o.tokens,t)))}}return e}use(...r){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return r.forEach(e=>{const n={...e};if(n.async=this.defaults.async||n.async||!1,e.extensions&&(e.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const i=t.renderers[s.name];t.renderers[s.name]=i?function(...o){let l=s.renderer.apply(this,o);return l===!1&&(l=i.apply(this,o)),l}:s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const i=t[s.level];i?i.unshift(s.tokenizer):t[s.level]=[s.tokenizer],s.start&&(s.level==="block"?t.startBlock?t.startBlock.push(s.start):t.startBlock=[s.start]:s.level==="inline"&&(t.startInline?t.startInline.push(s.start):t.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(t.childTokens[s.name]=s.childTokens)}),n.extensions=t),e.renderer){const s=this.defaults.renderer||new Be(this.defaults);for(const i in e.renderer){if(!(i in s))throw new Error(`renderer '${i}' does not exist`);if(i==="options")continue;const o=i,l=e.renderer[o],a=s[o];s[o]=(...c)=>{let u=l.apply(s,c);return u===!1&&(u=a.apply(s,c)),u||""}}n.renderer=s}if(e.tokenizer){const s=this.defaults.tokenizer||new Ee(this.defaults);for(const i in e.tokenizer){if(!(i in s))throw new Error(`tokenizer '${i}' does not exist`);if(["options","rules","lexer"].includes(i))continue;const o=i,l=e.tokenizer[o],a=s[o];s[o]=(...c)=>{let u=l.apply(s,c);return u===!1&&(u=a.apply(s,c)),u}}n.tokenizer=s}if(e.hooks){const s=this.defaults.hooks||new de;for(const i in e.hooks){if(!(i in s))throw new Error(`hook '${i}' does not exist`);if(i==="options")continue;const o=i,l=e.hooks[o],a=s[o];de.passThroughHooks.has(i)?s[o]=c=>{if(this.defaults.async)return Promise.resolve(l.call(s,c)).then(d=>a.call(s,d));const u=l.call(s,c);return a.call(s,u)}:s[o]=(...c)=>{let u=l.apply(s,c);return u===!1&&(u=a.apply(s,c)),u}}n.hooks=s}if(e.walkTokens){const s=this.defaults.walkTokens,i=e.walkTokens;n.walkTokens=function(o){let l=[];return l.push(i.call(this,o)),s&&(l=l.concat(s.call(this,o))),l}}this.defaults={...this.defaults,...n}}),this}setOptions(r){return this.defaults={...this.defaults,...r},this}lexer(r,t){return Ut.lex(r,t??this.defaults)}parser(r,t){return Ht.parse(r,t??this.defaults)}},le=new WeakSet,je=function(r,t){return(e,n)=>{const s={...n},i={...this.defaults,...s};this.defaults.async===!0&&s.async===!1&&(i.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),i.async=!0);const o=De(this,le,wi).call(this,!!i.silent,!!i.async);if(e==null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof e!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected"));if(i.hooks&&(i.hooks.options=i),i.async)return Promise.resolve(i.hooks?i.hooks.preprocess(e):e).then(l=>r(l,i)).then(l=>i.hooks?i.hooks.processAllTokens(l):l).then(l=>i.walkTokens?Promise.all(this.walkTokens(l,i.walkTokens)).then(()=>l):l).then(l=>t(l,i)).then(l=>i.hooks?i.hooks.postprocess(l):l).catch(o);try{i.hooks&&(e=i.hooks.preprocess(e));let l=r(e,i);i.hooks&&(l=i.hooks.processAllTokens(l)),i.walkTokens&&this.walkTokens(l,i.walkTokens);let a=t(l,i);return i.hooks&&(a=i.hooks.postprocess(a)),a}catch(l){return o(l)}}},wi=function(r,t){return e=>{if(e.message+=`
Please report this to https://github.com/markedjs/marked.`,r){const n="<p>An error occurred:</p><pre>"+St(e.message+"",!0)+"</pre>";return t?Promise.resolve(n):n}if(t)return Promise.reject(e);throw e}},Vs);function Dt(r,t){return ie.parse(r,t)}Dt.options=Dt.setOptions=function(r){return ie.setOptions(r),Dt.defaults=ie.defaults,as(Dt.defaults),Dt},Dt.getDefaults=Na,Dt.defaults=ae,Dt.use=function(...r){return ie.use(...r),Dt.defaults=ie.defaults,as(Dt.defaults),Dt},Dt.walkTokens=function(r,t){return ie.walkTokens(r,t)},Dt.parseInline=ie.parseInline,Dt.Parser=Ht,Dt.parser=Ht.parse,Dt.Renderer=Be,Dt.TextRenderer=nn,Dt.Lexer=Ut,Dt.lexer=Ut.lex,Dt.Tokenizer=Ee,Dt.Hooks=de,Dt.parse=Dt,Dt.options,Dt.setOptions,Dt.use,Dt.walkTokens,Dt.parseInline,Ht.parse,Ut.lex;const rc=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,oc=Object.hasOwnProperty;class lc{constructor(){this.occurrences,this.reset()}slug(t,e){const n=this;let s=function(o,l){return typeof o!="string"?"":(l||(o=o.toLowerCase()),o.replace(rc,"").replace(/ /g,"-"))}(t,e===!0);const i=s;for(;oc.call(n.occurrences,s);)n.occurrences[i]++,s=i+"-"+n.occurrences[i];return n.occurrences[s]=0,s}reset(){this.occurrences=Object.create(null)}}function ac(r){let t,e;return t=new Oe({props:{tokens:r[0],renderers:r[1],options:r[2]}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,[s]){const i={};1&s&&(i.tokens=n[0]),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function cc(r,t,e){(function(){const c=console.warn;console.warn=u=>{u.includes("unknown prop")||u.includes("unexpected slot")||c(u)},Gt(()=>{console.warn=c})})();let n,s,i,{source:o}=t,{options:l={}}=t,{renderers:a={}}=t;return r.$$set=c=>{"source"in c&&e(3,o=c.source),"options"in c&&e(4,l=c.options),"renderers"in c&&e(5,a=c.renderers)},r.$$.update=()=>{var c;56&r.$$.dirty&&(e(0,(c=o,n=new Ut().lex(c))),e(1,s={heading:_l,blockquote:Ll,list:Ul,list_item:Hl,br:Ql,code:Yl,codespan:ta,table:sa,html:oa,paragraph:ca,link:pa,text:ga,def:Da,del:Ca,em:ya,hr:ba,strong:Ba,image:Ma,space:ls,escape:ls,...a}),e(2,i={baseUrl:"/",slugger:new lc,...l}))},[n,s,i,o,l,a]}class uc extends tt{constructor(t){super(),et(this,t,cc,ac,K,{source:3,options:4,renderers:5})}}const dc=r=>({}),ms=r=>({}),pc=r=>({}),Ds=r=>({}),fc=r=>({}),Fs=r=>({});function $c(r){let t,e,n,s,i,o,l,a,c,u,d,p;const C=r[13].topBarLeft,x=_t(C,r,r[12],Fs),$=r[13].topBarRight,k=_t($,r,r[12],Ds);function _(w){r[16](w)}let B={options:{lineNumbers:"off",wrappingIndent:"same",padding:r[5],wordWrap:r[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"},text:r[3].text,lang:r[4]||r[3].lang,height:r[6]};r[0]!==void 0&&(B.editorInstance=r[0]),o=new Ui({props:B}),bt.push(()=>ee(o,"editorInstance",_));const D=r[13].actionsBar,F=_t(D,r,r[12],ms);return{c(){t=b("div"),e=b("div"),n=b("div"),x&&x.c(),s=I(),k&&k.c(),i=I(),z(o.$$.fragment),a=I(),c=b("div"),F&&F.c(),v(n,"class","c-codeblock__top-bar-left svelte-mexfz1"),v(e,"class","c-codeblock__top-bar-anchor monaco-component svelte-mexfz1"),v(c,"class","c-codeblock__actions-bar-anchor svelte-mexfz1"),v(t,"class","c-codeblock svelte-mexfz1"),v(t,"role","button"),v(t,"tabindex","0")},m(w,E){h(w,t,E),R(t,e),R(e,n),x&&x.m(n,null),R(e,s),k&&k.m(e,null),R(t,i),L(o,t,null),R(t,a),R(t,c),F&&F.m(c,null),r[17](t),u=!0,d||(p=[Qt(window,"focus",r[15]),Qt(t,"mouseenter",r[14])],d=!0)},p(w,[E]){x&&x.p&&(!u||4096&E)&&Bt(x,C,w,w[12],u?Lt(C,w[12],E,fc):zt(w[12]),Fs),k&&k.p&&(!u||4096&E)&&Bt(k,$,w,w[12],u?Lt($,w[12],E,pc):zt(w[12]),Ds);const O={};36&E&&(O.options={lineNumbers:"off",wrappingIndent:"same",padding:w[5],wordWrap:w[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"}),8&E&&(O.text=w[3].text),24&E&&(O.lang=w[4]||w[3].lang),64&E&&(O.height=w[6]),!l&&1&E&&(l=!0,O.editorInstance=w[0],ne(()=>l=!1)),o.$set(O),F&&F.p&&(!u||4096&E)&&Bt(F,D,w,w[12],u?Lt(D,w[12],E,dc):zt(w[12]),ms)},i(w){u||(f(x,w),f(k,w),f(o.$$.fragment,w),f(F,w),u=!0)},o(w){g(x,w),g(k,w),g(o.$$.fragment,w),g(F,w),u=!1},d(w){w&&m(t),x&&x.d(w),k&&k.d(w),M(o),F&&F.d(w),r[17](null),d=!1,ze(p)}}}function gc(r,t,e){let n,{$$slots:s={},$$scope:i}=t,{scroll:o=!1}=t,{token:l}=t,{language:a}=t,{padding:c={top:0,bottom:0}}=t,{editorInstance:u}=t,{element:d}=t,{height:p}=t;const C=Ge.getContext().monaco;Rt(r,C,_=>e(18,n=_));const x=Je(),$=()=>{if(!u)return;const _=u.getSelections();if(!(_!=null&&_.length))return;const B=u.getModel();if(_.map(D=>(B==null?void 0:B.getValueLengthInRange(D))||0).reduce((D,F)=>D+F,0)!==0)return _.sort(n==null?void 0:n.Range.compareRangesUsingStarts).map(D=>(B==null?void 0:B.getValueInRange(D))||"").join(`
`)},k=()=>{if(u)return u.getValue()||""};return r.$$set=_=>{"scroll"in _&&e(2,o=_.scroll),"token"in _&&e(3,l=_.token),"language"in _&&e(4,a=_.language),"padding"in _&&e(5,c=_.padding),"editorInstance"in _&&e(0,u=_.editorInstance),"element"in _&&e(1,d=_.element),"height"in _&&e(6,p=_.height),"$$scope"in _&&e(12,i=_.$$scope)},r.$$.update=()=>{var _;32&r.$$.dirty&&(_=c,u==null||u.updateOptions({padding:_})),65&r.$$.dirty&&(u==null||u.updateOptions({scrollbar:{vertical:p!==void 0?"auto":"hidden"}}))},[u,d,o,l,a,c,p,C,x,()=>u&&($()||k())||"",$,k,i,s,function(_){Bi.call(this,r,_)},()=>x.requestLayout(),function(_){u=_,e(0,u)},function(_){bt[_?"unshift":"push"](()=>{d=_,e(1,d)})}]}class xs extends tt{constructor(t){super(),et(this,t,gc,$c,K,{scroll:2,token:3,language:4,padding:5,editorInstance:0,element:1,height:6,getSelectionOrContents:9,getSelections:10,getContents:11})}get getSelectionOrContents(){return this.$$.ctx[9]}get getSelections(){return this.$$.ctx[10]}get getContents(){return this.$$.ctx[11]}}const hc=r=>({codespanContents:2&r}),Cs=r=>({codespanContents:r[1]});function mc(r){let t,e,n;const s=r[4].default,i=_t(s,r,r[3],Cs),o=i||function(l){let a;return{c(){a=N(l[1])},m(c,u){h(c,a,u)},p(c,u){2&u&&rt(a,c[1])},d(c){c&&m(a)}}}(r);return{c(){t=b("span"),e=b("code"),o&&o.c(),v(e,"class","markdown-codespan svelte-1dofrdh")},m(l,a){h(l,t,a),R(t,e),o&&o.m(e,null),r[5](t),n=!0},p(l,[a]){i?i.p&&(!n||10&a)&&Bt(i,s,l,l[3],n?Lt(s,l[3],a,hc):zt(l[3]),Cs):o&&o.p&&(!n||2&a)&&o.p(l,n?a:-1)},i(l){n||(f(o,l),n=!0)},o(l){g(o,l),n=!1},d(l){l&&m(t),o&&o.d(l),r[5](null)}}}function Dc(r,t,e){let n,{$$slots:s={},$$scope:i}=t,{token:o}=t,{element:l}=t;return r.$$set=a=>{"token"in a&&e(2,o=a.token),"element"in a&&e(0,l=a.element),"$$scope"in a&&e(3,i=a.$$scope)},r.$$.update=()=>{4&r.$$.dirty&&e(1,n=o.raw.slice(1,o.raw.length-1))},[l,n,o,i,s,function(a){bt[a?"unshift":"push"](()=>{l=a,e(0,l)})}]}class ws extends tt{constructor(t){super(),et(this,t,Dc,mc,K,{token:2,element:0})}}function Fc(r){let t,e,n,s,i=r[0].text+"";return{c(){t=b("span"),e=N("~"),n=N(i),s=N("~")},m(o,l){h(o,t,l),R(t,e),R(t,n),R(t,s)},p(o,[l]){1&l&&i!==(i=o[0].text+"")&&rt(n,i)},i:st,o:st,d(o){o&&m(t)}}}function xc(r,t,e){let{token:n}=t;return r.$$set=s=>{"token"in s&&e(0,n=s.token)},[n]}class vs extends tt{constructor(t){super(),et(this,t,xc,Fc,K,{token:0})}}function Cc(r){let t,e;const n=r[1].default,s=_t(n,r,r[0],null);return{c(){t=b("p"),s&&s.c(),v(t,"class","augment-markdown-paragraph svelte-1edcdk9")},m(i,o){h(i,t,o),s&&s.m(t,null),e=!0},p(i,[o]){s&&s.p&&(!e||1&o)&&Bt(s,n,i,i[0],e?Lt(n,i[0],o,null):zt(i[0]),null)},i(i){e||(f(s,i),e=!0)},o(i){g(s,i),e=!1},d(i){i&&m(t),s&&s.d(i)}}}function wc(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(0,s=i.$$scope)},[s,n]}class ys extends tt{constructor(t){super(),et(this,t,wc,Cc,K,{})}}function vc(r){let t,e,n;return e=new uc({props:{source:r[0],renderers:{codespan:ws,code:xs,paragraph:ys,del:vs,...r[1]}}}),{c(){t=b("div"),z(e.$$.fragment),v(t,"class","c-markdown svelte-n6ddeo")},m(s,i){h(s,t,i),L(e,t,null),n=!0},p(s,[i]){const o={};1&i&&(o.source=s[0]),2&i&&(o.renderers={codespan:ws,code:xs,paragraph:ys,del:vs,...s[1]}),e.$set(o)},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function yc(r,t,e){let{markdown:n}=t,{renderers:s={}}=t;return r.$$set=i=>{"markdown"in i&&e(0,n=i.markdown),"renderers"in i&&e(1,s=i.renderers)},[n,s]}class kc extends tt{constructor(t){super(),et(this,t,yc,vc,K,{markdown:0,renderers:1})}}function Ac(r){let t;return{c(){t=N(r[1])},m(e,n){h(e,t,n)},p(e,n){2&n&&rt(t,e[1])},d(e){e&&m(t)}}}function bc(r){let t;return{c(){t=N(r[1])},m(e,n){h(e,t,n)},p(e,n){2&n&&rt(t,e[1])},d(e){e&&m(t)}}}function Ec(r){let t,e,n;function s(l,a){return l[2]?bc:Ac}let i=s(r),o=i(r);return{c(){t=b("span"),e=b("code"),o.c(),v(e,"class","markdown-codespan svelte-11ta4gi"),v(e,"style",n=r[2]?`background-color: ${r[1]}; color: ${r[3]?"white":"black"}`:""),Ft(e,"markdown-string",r[4])},m(l,a){h(l,t,a),R(t,e),o.m(e,null),r[6](t)},p(l,[a]){i===(i=s(l))&&o?o.p(l,a):(o.d(1),o=i(l),o&&(o.c(),o.m(e,null))),14&a&&n!==(n=l[2]?`background-color: ${l[1]}; color: ${l[3]?"white":"black"}`:"")&&v(e,"style",n),16&a&&Ft(e,"markdown-string",l[4])},i:st,o:st,d(l){l&&m(t),o.d(),r[6](null)}}}function _c(r,t,e){let n,s,i,o,{token:l}=t,{element:a}=t;return r.$$set=c=>{"token"in c&&e(5,l=c.token),"element"in c&&e(0,a=c.element)},r.$$.update=()=>{32&r.$$.dirty&&e(1,n=l.raw.slice(1,l.raw.length-1)),2&r.$$.dirty&&e(4,s=n.startsWith('"')),2&r.$$.dirty&&e(2,i=/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(n)),6&r.$$.dirty&&e(3,o=i&&function(c){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(c))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let u,d,p;return c.length===4?(u=parseInt(c.charAt(1),16),d=parseInt(c.charAt(2),16),p=parseInt(c.charAt(3),16),u*=17,d*=17,p*=17):(u=parseInt(c.slice(1,3),16),d=parseInt(c.slice(3,5),16),p=parseInt(c.slice(5,7),16)),.299*u+.587*d+.114*p<130}(n))},[a,n,i,o,s,l,function(c){bt[c?"unshift":"push"](()=>{a=c,e(0,a)})}]}class Bc extends tt{constructor(t){super(),et(this,t,_c,Ec,K,{token:5,element:0})}}function zc(r){let t,e;return t=new kc({props:{markdown:r[1](r[0]),renderers:r[2]}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,[s]){const i={};1&s&&(i.markdown=n[1](n[0])),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function Lc(r,t,e){let{markdown:n}=t;const s={codespan:Bc};return r.$$set=i=>{"markdown"in i&&e(0,n=i.markdown)},[n,i=>i.replace(/`?#[0-9a-fA-F]{3,6}`?/g,o=>o.startsWith("`")?o:`\`${o}\``),s]}class Mc extends tt{constructor(t){super(),et(this,t,Lc,zc,K,{markdown:0})}}function ks(r,t,e){const n=r.slice();return n[4]=t[e],n}function Rc(r){let t;return{c(){t=N(`The following files will have merge conflicts if applied locally. Conflict markers will be
        added to the file which can be resolved manually after applying.`)},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function Oc(r){let t;return{c(){t=N("The following files have merge conflicts that need to be resolved manually.")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function Nc(r){let t,e,n,s;return t=new li({props:{filename:r[4]}}),n=new Ji({props:{filepath:r[4]}}),{c(){z(t.$$.fragment),e=I(),z(n.$$.fragment)},m(i,o){L(t,i,o),h(i,e,o),L(n,i,o),s=!0},p(i,o){const l={};1&o&&(l.filename=i[4]),t.$set(l);const a={};1&o&&(a.filepath=i[4]),n.$set(a)},i(i){s||(f(t.$$.fragment,i),f(n.$$.fragment,i),s=!0)},o(i){g(t.$$.fragment,i),g(n.$$.fragment,i),s=!1},d(i){i&&m(e),M(t,i),M(n,i)}}}function qc(r){let t,e;return t=new Qe({}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function Sc(r){let t,e;return t=new pe({props:{size:1,variant:"ghost-block",color:"neutral",$$slots:{default:[qc]},$$scope:{ctx:r}}}),t.$on("click",function(){return r[3](r[4])}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){r=n;const i={};128&s&&(i.$$scope={dirty:s,ctx:r}),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function As(r){let t,e,n,s,i,o;return e=new Vt({props:{content:r[4],nested:!0,$$slots:{default:[Nc]},$$scope:{ctx:r}}}),s=new Vt({props:{content:"Open file",$$slots:{default:[Sc]},$$scope:{ctx:r}}}),{c(){t=b("div"),z(e.$$.fragment),n=I(),z(s.$$.fragment),i=I(),v(t,"class","c-conflicts-card__file svelte-1bce35u")},m(l,a){h(l,t,a),L(e,t,null),R(t,n),L(s,t,null),R(t,i),o=!0},p(l,a){const c={};1&a&&(c.content=l[4]),129&a&&(c.$$scope={dirty:a,ctx:l}),e.$set(c);const u={};133&a&&(u.$$scope={dirty:a,ctx:l}),s.$set(u)},i(l){o||(f(e.$$.fragment,l),f(s.$$.fragment,l),o=!0)},o(l){g(e.$$.fragment,l),g(s.$$.fragment,l),o=!1},d(l){l&&m(t),M(e),M(s)}}}function Tc(r){let t,e,n,s,i,o,l,a,c,u,d,p,C,x=r[0].size+"";function $(w,E){return w[1]?Oc:Rc}n=new ei({});let k=$(r),_=k(r),B=dt(r[0]),D=[];for(let w=0;w<B.length;w+=1)D[w]=As(ks(r,B,w));const F=w=>g(D[w],1,1,()=>{D[w]=null});return{c(){t=b("div"),e=b("div"),z(n.$$.fragment),s=I(),i=b("span"),o=N("Conflicts ("),l=N(x),a=N(")"),c=I(),u=b("div"),_.c(),d=I();for(let w=0;w<D.length;w+=1)D[w].c();p=vt(),v(e,"class","c-conflicts-card__icon svelte-1bce35u"),v(i,"class","c-conflicts-card__title svelte-1bce35u"),v(t,"class","c-conflicts-card__header svelte-1bce35u"),v(u,"class","c-conflicts-card__description svelte-1bce35u")},m(w,E){h(w,t,E),R(t,e),L(n,e,null),R(t,s),R(t,i),R(i,o),R(i,l),R(i,a),h(w,c,E),h(w,u,E),_.m(u,null),h(w,d,E);for(let O=0;O<D.length;O+=1)D[O]&&D[O].m(w,E);h(w,p,E),C=!0},p(w,E){if((!C||1&E)&&x!==(x=w[0].size+"")&&rt(l,x),k!==(k=$(w))&&(_.d(1),_=k(w),_&&(_.c(),_.m(u,null))),5&E){let O;for(B=dt(w[0]),O=0;O<B.length;O+=1){const S=ks(w,B,O);D[O]?(D[O].p(S,E),f(D[O],1)):(D[O]=As(S),D[O].c(),f(D[O],1),D[O].m(p.parentNode,p))}for(j(),O=B.length;O<D.length;O+=1)F(O);W()}},i(w){if(!C){f(n.$$.fragment,w);for(let E=0;E<B.length;E+=1)f(D[E]);C=!0}},o(w){g(n.$$.fragment,w),D=D.filter(Boolean);for(let E=0;E<D.length;E+=1)g(D[E]);C=!1},d(w){w&&(m(t),m(c),m(u),m(d),m(p)),M(n),_.d(),Mt(D,w)}}}function Pc(r){let t,e,n;return e=new ji({props:{includeBackground:!1,$$slots:{default:[Tc]},$$scope:{ctx:r}}}),{c(){t=b("div"),z(e.$$.fragment),v(t,"class","c-conflicts-card")},m(s,i){h(s,t,i),L(e,t,null),n=!0},p(s,[i]){const o={};135&i&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function Ic(r,t,e){let{files:n}=t,{hasAppliedAll:s}=t,{onOpenFile:i}=t;return r.$$set=o=>{"files"in o&&e(0,n=o.files),"hasAppliedAll"in o&&e(1,s=o.hasAppliedAll),"onOpenFile"in o&&e(2,i=o.onOpenFile)},[n,s,i,o=>i==null?void 0:i(o)]}class Uc extends tt{constructor(t){super(),et(this,t,Ic,Pc,K,{files:0,hasAppliedAll:1,onOpenFile:2})}}function Vc(r){let t,e,n,s;return e=new Ti({props:{token:{type:"codespan",text:"`git stash`",raw:"`git stash`"}}}),{c(){t=N(`There are unstaged changes in your working directory. Please commit your changes or we will
      run
      `),z(e.$$.fragment),n=N(`
      to stash your changes before applying changes from the remote agent.`)},m(i,o){h(i,t,o),L(e,i,o),h(i,n,o),s=!0},p:st,i(i){s||(f(e.$$.fragment,i),s=!0)},o(i){g(e.$$.fragment,i),s=!1},d(i){i&&(m(t),m(n)),M(e,i)}}}function bs(r){let t,e;return t=new $t({props:{$$slots:{default:[Zc]},$$scope:{ctx:r}}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};130&s&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function Zc(r){let t;return{c(){t=N(r[1])},m(e,n){h(e,t,n)},p(e,n){2&n&&rt(t,e[1])},d(e){e&&m(t)}}}function Hc(r){let t,e,n,s;e=new $t({props:{$$slots:{default:[Vc]},$$scope:{ctx:r}}});let i=r[1]&&bs(r);return{c(){t=b("div"),z(e.$$.fragment),n=I(),i&&i.c(),v(t,"class","c-unstaged-changes-modal__body svelte-9eyy34")},m(o,l){h(o,t,l),L(e,t,null),R(t,n),i&&i.m(t,null),s=!0},p(o,l){const a={};128&l&&(a.$$scope={dirty:l,ctx:o}),e.$set(a),o[1]?i?(i.p(o,l),2&l&&f(i,1)):(i=bs(o),i.c(),f(i,1),i.m(t,null)):i&&(j(),g(i,1,1,()=>{i=null}),W())},i(o){s||(f(e.$$.fragment,o),f(i),s=!0)},o(o){g(e.$$.fragment,o),g(i),s=!1},d(o){o&&m(t),M(e),i&&i.d()}}}function Es(r){let t,e,n;return e=new Me({props:{size:1}}),{c(){t=b("div"),z(e.$$.fragment),v(t,"class","c-unstaged-changes-modal__stash-button-loading svelte-9eyy34")},m(s,i){h(s,t,i),L(e,t,null),n=!0},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function jc(r){let t,e,n,s=r[2]&&Es();return{c(){s&&s.c(),t=I(),e=b("span"),e.textContent="Stash & Apply Locally",v(e,"class","c-unstaged-changes-modal__stash-button-text svelte-9eyy34"),Ft(e,"loading",r[2])},m(i,o){s&&s.m(i,o),h(i,t,o),h(i,e,o),n=!0},p(i,o){i[2]?s?4&o&&f(s,1):(s=Es(),s.c(),f(s,1),s.m(t.parentNode,t)):s&&(j(),g(s,1,1,()=>{s=null}),W()),(!n||4&o)&&Ft(e,"loading",i[2])},i(i){n||(f(s),n=!0)},o(i){g(s),n=!1},d(i){i&&(m(t),m(e)),s&&s.d(i)}}}function Wc(r){let t;return{c(){t=N("Abort")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function Qc(r){let t,e,n,s,i;return e=new Tt({props:{variant:"solid",color:"accent",disabled:!!r[1]||r[2],$$slots:{default:[jc]},$$scope:{ctx:r}}}),e.$on("click",r[3]),s=new Tt({props:{variant:"solid",color:"neutral",disabled:r[2],$$slots:{default:[Wc]},$$scope:{ctx:r}}}),s.$on("click",r[4]),{c(){t=b("div"),z(e.$$.fragment),n=I(),z(s.$$.fragment),v(t,"class","c-unstaged-changes-modal__footer svelte-9eyy34"),v(t,"slot","footer")},m(o,l){h(o,t,l),L(e,t,null),R(t,n),L(s,t,null),i=!0},p(o,l){const a={};6&l&&(a.disabled=!!o[1]||o[2]),132&l&&(a.$$scope={dirty:l,ctx:o}),e.$set(a);const c={};4&l&&(c.disabled=o[2]),128&l&&(c.$$scope={dirty:l,ctx:o}),s.$set(c)},i(o){i||(f(e.$$.fragment,o),f(s.$$.fragment,o),i=!0)},o(o){g(e.$$.fragment,o),g(s.$$.fragment,o),i=!1},d(o){o&&m(t),M(e),M(s)}}}function Gc(r){let t,e;return t=new Yi({props:{show:r[0],title:"Unstaged changes",$$slots:{footer:[Qc],default:[Hc]},$$scope:{ctx:r}}}),t.$on("cancel",r[4]),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,[s]){const i={};1&s&&(i.show=n[0]),134&s&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function Jc(r,t,e){let{showModal:n=!1}=t,{applyAllChanges:s}=t;const i=te(oe.key);let o,l=!1;return r.$$set=a=>{"showModal"in a&&e(0,n=a.showModal),"applyAllChanges"in a&&e(5,s=a.applyAllChanges)},[n,o,l,async function(){if(e(2,l=!0),!await i.stashUnstagedChanges())return e(1,o="Failed to stash changes. Please manually stash or commit your unstaged changes."),void e(2,l=!1);await new Promise(a=>setTimeout(a,1500)),e(1,o=void 0),e(0,n=!1),s(),e(2,l=!1)},function(){e(0,n=!1),e(1,o=void 0)},s]}class Yc extends tt{constructor(t){super(),et(this,t,Jc,Gc,K,{showModal:0,applyAllChanges:5})}}const{Boolean:vi,Map:Xc}=Li;function _s(r,t,e){const n=r.slice();return n[55]=t[e],n[56]=t,n[57]=e,n}function Bs(r,t,e){const n=r.slice();return n[58]=t[e],n[59]=t,n[60]=e,n}function zs(r,t,e){const n=r.slice();return n[61]=t[e],n[62]=t,n[63]=e,n}function Pe(r){const t=r.slice(),e=t[11]?t[6]:t[23];return t[64]=e,t}function Ls(r){let t,e,n,s,i,o,l,a;e=new ai({}),o=new Tt({props:{variant:"ghost",size:1,$$slots:{default:[Kc]},$$scope:{ctx:r}}}),o.$on("click",r[34]);let c=r[4]&&Ms(r);return{c(){t=b("div"),z(e.$$.fragment),n=I(),s=N(r[20]),i=I(),z(o.$$.fragment),l=I(),c&&c.c(),v(t,"class","c-diff-view__error svelte-ibi4q5")},m(u,d){h(u,t,d),L(e,t,null),R(t,n),R(t,s),R(t,i),L(o,t,null),R(t,l),c&&c.m(t,null),a=!0},p(u,d){(!a||1048576&d[0])&&rt(s,u[20]);const p={};8&d[2]&&(p.$$scope={dirty:d,ctx:u}),o.$set(p),u[4]?c?(c.p(u,d),16&d[0]&&f(c,1)):(c=Ms(u),c.c(),f(c,1),c.m(t,null)):c&&(j(),g(c,1,1,()=>{c=null}),W())},i(u){a||(f(e.$$.fragment,u),f(o.$$.fragment,u),f(c),a=!0)},o(u){g(e.$$.fragment,u),g(o.$$.fragment,u),g(c),a=!1},d(u){u&&m(t),M(e),M(o),c&&c.d()}}}function Kc(r){let t;return{c(){t=N("Retry")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function Ms(r){let t,e;return t=new Tt({props:{variant:"ghost",size:1,$$slots:{default:[tu]},$$scope:{ctx:r}}}),t.$on("click",function(){We(r[4])&&r[4].apply(this,arguments)}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){r=n;const i={};8&s[2]&&(i.$$scope={dirty:s,ctx:r}),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function tu(r){let t;return{c(){t=N("Render as list")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function eu(r){let t,e,n,s,i,o,l,a,c,u,d,p,C,x=r[1]&&r[2]!==r[1]&&Rs(r),$=r[2]&&Os(r);o=new $t({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[lu]},$$scope:{ctx:r}}}),a=new $i({props:{changedFiles:r[0]}});const k=[cu,au],_=[];function B(D,F){return D[18]&&D[17].length===0?0:D[7]&&D[7].length>0?1:-1}return~(d=B(r))&&(p=_[d]=k[d](r)),{c(){t=b("div"),e=b("div"),n=b("div"),x&&x.c(),s=I(),$&&$.c(),i=I(),z(o.$$.fragment),l=I(),z(a.$$.fragment),c=I(),u=b("div"),p&&p.c(),v(n,"class","c-diff-view__tree__header svelte-ibi4q5"),v(e,"class","c-diff-view__tree svelte-ibi4q5"),v(u,"class","c-diff-view__explanation svelte-ibi4q5"),v(t,"class","c-diff-view__layout svelte-ibi4q5")},m(D,F){h(D,t,F),R(t,e),R(e,n),x&&x.m(n,null),R(n,s),$&&$.m(n,null),R(n,i),L(o,n,null),R(n,l),L(a,n,null),R(t,c),R(t,u),~d&&_[d].m(u,null),C=!0},p(D,F){D[1]&&D[2]!==D[1]?x?(x.p(D,F),6&F[0]&&f(x,1)):(x=Rs(D),x.c(),f(x,1),x.m(n,s)):x&&(j(),g(x,1,1,()=>{x=null}),W()),D[2]?$?($.p(D,F),4&F[0]&&f($,1)):($=Os(D),$.c(),f($,1),$.m(n,i)):$&&(j(),g($,1,1,()=>{$=null}),W());const w={};8&F[2]&&(w.$$scope={dirty:F,ctx:D}),o.$set(w);const E={};1&F[0]&&(E.changedFiles=D[0]),a.$set(E);let O=d;d=B(D),d===O?~d&&_[d].p(D,F):(p&&(j(),g(_[O],1,1,()=>{_[O]=null}),W()),~d?(p=_[d],p?p.p(D,F):(p=_[d]=k[d](D),p.c()),f(p,1),p.m(u,null)):p=null)},i(D){C||(f(x),f($),f(o.$$.fragment,D),f(a.$$.fragment,D),f(p),C=!0)},o(D){g(x),g($),g(o.$$.fragment,D),g(a.$$.fragment,D),g(p),C=!1},d(D){D&&m(t),x&&x.d(),$&&$.d(),M(o),M(a),~d&&_[d].d()}}}function nu(r){let t,e,n;return e=new $t({props:{size:2,color:"secondary",$$slots:{default:[Ou]},$$scope:{ctx:r}}}),{c(){t=b("div"),z(e.$$.fragment),v(t,"class","c-diff-view__empty svelte-ibi4q5")},m(s,i){h(s,t,i),L(e,t,null),n=!0},p(s,i){const o={};8&i[2]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function Rs(r){let t,e,n,s;return t=new $t({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[su]},$$scope:{ctx:r}}}),n=new $t({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[iu]},$$scope:{ctx:r}}}),{c(){z(t.$$.fragment),e=I(),z(n.$$.fragment)},m(i,o){L(t,i,o),h(i,e,o),L(n,i,o),s=!0},p(i,o){const l={};8&o[2]&&(l.$$scope={dirty:o,ctx:i}),t.$set(l);const a={};2&o[0]|8&o[2]&&(a.$$scope={dirty:o,ctx:i}),n.$set(a)},i(i){s||(f(t.$$.fragment,i),f(n.$$.fragment,i),s=!0)},o(i){g(t.$$.fragment,i),g(n.$$.fragment,i),s=!1},d(i){i&&m(e),M(t,i),M(n,i)}}}function su(r){let t;return{c(){t=N("Changes from agent")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function iu(r){let t;return{c(){t=N(r[1])},m(e,n){h(e,t,n)},p(e,n){2&n[0]&&rt(t,e[1])},d(e){e&&m(t)}}}function Os(r){let t,e,n,s;return t=new $t({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[ru]},$$scope:{ctx:r}}}),n=new $t({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[ou]},$$scope:{ctx:r}}}),{c(){z(t.$$.fragment),e=I(),z(n.$$.fragment)},m(i,o){L(t,i,o),h(i,e,o),L(n,i,o),s=!0},p(i,o){const l={};8&o[2]&&(l.$$scope={dirty:o,ctx:i}),t.$set(l);const a={};4&o[0]|8&o[2]&&(a.$$scope={dirty:o,ctx:i}),n.$set(a)},i(i){s||(f(t.$$.fragment,i),f(n.$$.fragment,i),s=!0)},o(i){g(t.$$.fragment,i),g(n.$$.fragment,i),s=!1},d(i){i&&m(e),M(t,i),M(n,i)}}}function ru(r){let t;return{c(){t=N("Last user prompt")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function ou(r){let t;return{c(){t=N(r[2])},m(e,n){h(e,t,n)},p(e,n){4&n[0]&&rt(t,e[2])},d(e){e&&m(t)}}}function lu(r){let t;return{c(){t=N("Changed files")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function au(r){let t,e,n=dt(r[7]),s=[];for(let o=0;o<n.length;o+=1)s[o]=Ps(_s(r,n,o));const i=o=>g(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();t=vt()},m(o,l){for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(o,l);h(o,t,l),e=!0},p(o,l){if(1793708008&l[0]|3&l[1]){let a;for(n=dt(o[7]),a=0;a<n.length;a+=1){const c=_s(o,n,a);s[a]?(s[a].p(c,l),f(s[a],1)):(s[a]=Ps(c),s[a].c(),f(s[a],1),s[a].m(t.parentNode,t))}for(j(),a=n.length;a<s.length;a+=1)i(a);W()}},i(o){if(!e){for(let l=0;l<n.length;l+=1)f(s[l]);e=!0}},o(o){s=s.filter(vi);for(let l=0;l<s.length;l+=1)g(s[l]);e=!1},d(o){o&&m(t),Mt(s,o)}}}function cu(r){let t,e,n,s,i;return e=new Vt({props:{content:r[10]?"Applying changes...":r[11]?"All changes applied":r[12]?"Apply all changes":"No changes to apply",$$slots:{default:[Ru]},$$scope:{ctx:r}}}),s=new Al({props:{count:2}}),{c(){t=b("div"),z(e.$$.fragment),n=I(),z(s.$$.fragment),v(t,"class","c-diff-view__controls svelte-ibi4q5")},m(o,l){h(o,t,l),L(e,t,null),h(o,n,l),L(s,o,l),i=!0},p(o,l){const a={};7168&l[0]&&(a.content=o[10]?"Applying changes...":o[11]?"All changes applied":o[12]?"Apply all changes":"No changes to apply"),15360&l[0]|8&l[2]&&(a.$$scope={dirty:l,ctx:o}),e.$set(a)},i(o){i||(f(e.$$.fragment,o),f(s.$$.fragment,o),i=!0)},o(o){g(e.$$.fragment,o),g(s.$$.fragment,o),i=!1},d(o){o&&(m(t),m(n)),M(e),M(s,o)}}}function uu(r){let t,e=r[55].title+"";return{c(){t=N(e)},m(n,s){h(n,t,s)},p(n,s){128&s[0]&&e!==(e=n[55].title+"")&&rt(t,e)},d(n){n&&m(t)}}}function du(r){let t;return{c(){t=b("div"),v(t,"class","c-diff-view__skeleton-title svelte-ibi4q5")},m(e,n){h(e,t,n)},p:st,d(e){e&&m(t)}}}function pu(r){let t,e;return t=new Mc({props:{markdown:r[55].description}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};128&s[0]&&(i.markdown=n[55].description),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function fu(r){let t,e,n;return{c(){t=b("div"),e=I(),n=b("div"),v(t,"class","c-diff-view__skeleton-text svelte-ibi4q5"),v(n,"class","c-diff-view__skeleton-text svelte-ibi4q5")},m(s,i){h(s,t,i),h(s,e,i),h(s,n,i)},p:st,i:st,o:st,d(s){s&&(m(t),m(e),m(n))}}}function $u(r){let t,e,n;return t=new Pi({}),{c(){z(t.$$.fragment),e=N(`
                        Expand All`)},m(s,i){L(t,s,i),h(s,e,i),n=!0},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t,s)}}}function gu(r){let t,e,n;return t=new Ii({}),{c(){z(t.$$.fragment),e=N(`
                        Collapse All`)},m(s,i){L(t,s,i),h(s,e,i),n=!0},i(s){n||(f(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&m(e),M(t,s)}}}function hu(r){let t,e,n,s;const i=[gu,$u],o=[];function l(a,c){return a[27]?1:0}return t=l(r),e=o[t]=i[t](r),{c(){e.c(),n=vt()},m(a,c){o[t].m(a,c),h(a,n,c),s=!0},p(a,c){let u=t;t=l(a),t!==u&&(j(),g(o[u],1,1,()=>{o[u]=null}),W(),e=o[t],e||(e=o[t]=i[t](a),e.c()),f(e,1),e.m(n.parentNode,n))},i(a){s||(f(e),s=!0)},o(a){g(e),s=!1},d(a){a&&m(n),o[t].d(a)}}}function mu(r){let t,e,n,s;return n=new fe({}),{c(){t=N(`Apply All
                          `),e=b("div"),z(n.$$.fragment),v(e,"class","c-diff-view__controls__icon svelte-ibi4q5")},m(i,o){h(i,t,o),h(i,e,o),L(n,e,null),s=!0},p:st,i(i){s||(f(n.$$.fragment,i),s=!0)},o(i){g(n.$$.fragment,i),s=!1},d(i){i&&(m(t),m(e)),M(n)}}}function Du(r){let t,e,n,s,i,o;e=new $t({props:{size:2,$$slots:{default:[xu]},$$scope:{ctx:r}}});const l=[wu,Cu],a=[];function c(u,d){return u[14]?0:1}return s=c(r),i=a[s]=l[s](r),{c(){t=b("div"),z(e.$$.fragment),n=I(),i.c(),v(t,"class","c-diff-view__applied svelte-ibi4q5")},m(u,d){h(u,t,d),L(e,t,null),R(t,n),a[s].m(t,null),o=!0},p(u,d){const p={};8&d[2]&&(p.$$scope={dirty:d,ctx:u}),e.$set(p);let C=s;s=c(u),s!==C&&(j(),g(a[C],1,1,()=>{a[C]=null}),W(),i=a[s],i||(i=a[s]=l[s](u),i.c()),f(i,1),i.m(t,null))},i(u){o||(f(e.$$.fragment,u),f(i),o=!0)},o(u){g(e.$$.fragment,u),g(i),o=!1},d(u){u&&m(t),M(e),a[s].d()}}}function Fu(r){let t,e,n,s,i;return e=new Me({props:{size:1,useCurrentColor:!0}}),s=new $t({props:{size:2,$$slots:{default:[vu]},$$scope:{ctx:r}}}),{c(){t=b("div"),z(e.$$.fragment),n=I(),z(s.$$.fragment),v(t,"class","c-diff-view__applying svelte-ibi4q5")},m(o,l){h(o,t,l),L(e,t,null),R(t,n),L(s,t,null),i=!0},p(o,l){const a={};8&l[2]&&(a.$$scope={dirty:l,ctx:o}),s.$set(a)},i(o){i||(f(e.$$.fragment,o),f(s.$$.fragment,o),i=!0)},o(o){g(e.$$.fragment,o),g(s.$$.fragment,o),i=!1},d(o){o&&m(t),M(e),M(s)}}}function xu(r){let t;return{c(){t=N("Applied")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function Cu(r){let t,e;return t=new Re({props:{iconName:"check"}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function wu(r){let t,e;return t=new ei({props:{slot:"rightIcon"}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function vu(r){let t;return{c(){t=N("Applying...")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function yu(r){let t,e,n,s;const i=[Fu,Du,mu],o=[];function l(a,c){return a[10]?0:a[11]?1:2}return t=l(r),e=o[t]=i[t](r),{c(){e.c(),n=vt()},m(a,c){o[t].m(a,c),h(a,n,c),s=!0},p(a,c){let u=t;t=l(a),t===u?o[t].p(a,c):(j(),g(o[u],1,1,()=>{o[u]=null}),W(),e=o[t],e?e.p(a,c):(e=o[t]=i[t](a),e.c()),f(e,1),e.m(n.parentNode,n))},i(a){s||(f(e),s=!0)},o(a){g(e),s=!1},d(a){a&&m(n),o[t].d(a)}}}function ku(r){let t,e;return t=new Tt({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[15],$$slots:{default:[yu]},$$scope:{ctx:r}}}),t.$on("click",r[32]),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};32768&s[0]&&(i.disabled=n[15]),19456&s[0]|8&s[2]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function Ns(r){let t,e;return t=new Uc({props:{files:r[64],hasAppliedAll:r[11],onOpenFile:r[3]}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};8390720&s[0]&&(i.files=n[64]),2048&s[0]&&(i.hasAppliedAll=n[11]),8&s[0]&&(i.onOpenFile=n[3]),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function Au(r){let t,e=r[58].title+"";return{c(){t=N(e)},m(n,s){h(n,t,s)},p(n,s){128&s[0]&&e!==(e=n[58].title+"")&&rt(t,e)},d(n){n&&m(t)}}}function bu(r){let t;return{c(){t=b("div"),v(t,"class","c-diff-view__skeleton-text svelte-ibi4q5")},m(e,n){h(e,t,n)},p:st,d(e){e&&m(t)}}}function qs(r){let t,e,n,s,i,o=r[58].warning+"";return e=new ai({}),{c(){t=b("div"),z(e.$$.fragment),n=I(),s=N(o),v(t,"class","c-diff-view__warning svelte-ibi4q5")},m(l,a){h(l,t,a),L(e,t,null),R(t,n),R(t,s),i=!0},p(l,a){(!i||128&a[0])&&o!==(o=l[58].warning+"")&&rt(s,o)},i(l){i||(f(e.$$.fragment,l),i=!0)},o(l){g(e.$$.fragment,l),i=!1},d(l){l&&m(t),M(e)}}}function Ss(r,t){let e,n,s,i,o,l=t[57],a=t[60],c=t[61];function u(...B){return t[42](t[61],...B)}function d(){return t[43](t[61])}function p(){return t[44](t[61])}function C(B){t[45](B,t[61])}const x=()=>t[46](n,l,a,c),$=()=>t[46](null,l,a,c);function k(B){t[47](B)}let _={path:t[61].path,change:t[61],descriptions:t[58].descriptions,isExpandedDefault:t[9][t[61].path]!==void 0?!t[9][t[61].path]:t[8],isApplying:t[16][t[61].path]==="pending",hasApplied:t[16][t[61].path]==="applied",onCodeChange:u,onApplyChanges:d,onOpenFile:t[3]?p:void 0,isAgentFromDifferentRepo:t[5]};return t[9][t[61].path]!==void 0&&(_.isCollapsed=t[9][t[61].path]),t[22]!==void 0&&(_.areDescriptionsVisible=t[22]),n=new Fl({props:_}),bt.push(()=>ee(n,"isCollapsed",C)),x(),bt.push(()=>ee(n,"areDescriptionsVisible",k)),{key:r,first:null,c(){e=b("div"),z(n.$$.fragment),v(e,"class","c-diff-view__changes-item svelte-ibi4q5"),this.first=e},m(B,D){h(B,e,D),L(n,e,null),o=!0},p(B,D){l===(t=B)[57]&&a===t[60]&&c===t[61]||($(),l=t[57],a=t[60],c=t[61],x());const F={};128&D[0]&&(F.path=t[61].path),128&D[0]&&(F.change=t[61]),128&D[0]&&(F.descriptions=t[58].descriptions),896&D[0]&&(F.isExpandedDefault=t[9][t[61].path]!==void 0?!t[9][t[61].path]:t[8]),65664&D[0]&&(F.isApplying=t[16][t[61].path]==="pending"),65664&D[0]&&(F.hasApplied=t[16][t[61].path]==="applied"),128&D[0]&&(F.onCodeChange=u),128&D[0]&&(F.onApplyChanges=d),136&D[0]&&(F.onOpenFile=t[3]?p:void 0),32&D[0]&&(F.isAgentFromDifferentRepo=t[5]),!s&&640&D[0]&&(s=!0,F.isCollapsed=t[9][t[61].path],ne(()=>s=!1)),!i&&4194304&D[0]&&(i=!0,F.areDescriptionsVisible=t[22],ne(()=>i=!1)),n.$set(F)},i(B){o||(f(n.$$.fragment,B),o=!0)},o(B){g(n.$$.fragment,B),o=!1},d(B){B&&m(e),$(),M(n)}}}function Ts(r){let t,e,n,s,i,o,l,a,c,u,d,p=[],C=new Xc;function x(F,w){return F[19]&&F[58].descriptions.length===0?bu:Au}i=new ko({props:{type:r[58].type}});let $=x(r),k=$(r),_=!r[19]&&r[58].warning&&qs(r),B=dt(r[58].changes);const D=F=>F[61].id;for(let F=0;F<B.length;F+=1){let w=zs(r,B,F),E=D(w);C.set(E,p[F]=Ss(E,w))}return{c(){t=b("div"),e=b("div"),n=b("div"),s=b("div"),z(i.$$.fragment),o=I(),l=b("h5"),k.c(),a=I(),_&&_.c(),c=I(),u=b("div");for(let F=0;F<p.length;F+=1)p[F].c();v(s,"class","c-diff-view__icon svelte-ibi4q5"),v(l,"class","c-diff-view__title svelte-ibi4q5"),v(n,"class","c-diff-view__content svelte-ibi4q5"),v(e,"class","c-diff-view__header svelte-ibi4q5"),v(u,"class","c-diff-view__changes svelte-ibi4q5"),v(t,"class","c-diff-view__subsection svelte-ibi4q5"),v(t,"id",`subsection-${r[57]}-${r[60]}`)},m(F,w){h(F,t,w),R(t,e),R(e,n),R(n,s),L(i,s,null),R(n,o),R(n,l),k.m(l,null),R(n,a),_&&_.m(n,null),R(t,c),R(t,u);for(let E=0;E<p.length;E+=1)p[E]&&p[E].m(u,null);d=!0},p(F,w){const E={};128&w[0]&&(E.type=F[58].type),i.$set(E),$===($=x(F))&&k?k.p(F,w):(k.d(1),k=$(F),k&&(k.c(),k.m(l,null))),!F[19]&&F[58].warning?_?(_.p(F,w),524416&w[0]&&f(_,1)):(_=qs(F),_.c(),f(_,1),_.m(n,null)):_&&(j(),g(_,1,1,()=>{_=null}),W()),1080099752&w[0]|1&w[1]&&(B=dt(F[58].changes),j(),p=Js(p,w,D,1,F,B,C,u,Ys,Ss,null,zs),W())},i(F){if(!d){f(i.$$.fragment,F),f(_);for(let w=0;w<B.length;w+=1)f(p[w]);d=!0}},o(F){g(i.$$.fragment,F),g(_);for(let w=0;w<p.length;w+=1)g(p[w]);d=!1},d(F){F&&m(t),M(i),k.d(),_&&_.d();for(let w=0;w<p.length;w+=1)p[w].d()}}}function Ps(r){let t,e,n,s,i,o,l,a,c,u,d,p,C;function x(T,Z){return T[19]&&T[55].title==="Loading..."?du:uu}let $=x(r),k=$(r);const _=[fu,pu],B=[];function D(T,Z){return T[19]&&T[55].description===""?0:1}l=D(r),a=B[l]=_[l](r);let F=r[57]===0&&function(T){let Z,Q,nt,ct,at;return Q=new Tt({props:{variant:"ghost-block",color:"neutral",size:2,$$slots:{default:[hu]},$$scope:{ctx:T}}}),Q.$on("click",T[29]),ct=new Vt({props:{content:T[25],$$slots:{default:[ku]},$$scope:{ctx:T}}}),{c(){Z=b("div"),z(Q.$$.fragment),nt=I(),z(ct.$$.fragment),v(Z,"class","c-diff-view__controls svelte-ibi4q5")},m(lt,J){h(lt,Z,J),L(Q,Z,null),R(Z,nt),L(ct,Z,null),at=!0},p(lt,J){const ft={};134217728&J[0]|8&J[2]&&(ft.$$scope={dirty:J,ctx:lt}),Q.$set(ft);const mt={};33554432&J[0]&&(mt.content=lt[25]),52224&J[0]|8&J[2]&&(mt.$$scope={dirty:J,ctx:lt}),ct.$set(mt)},i(lt){at||(f(Q.$$.fragment,lt),f(ct.$$.fragment,lt),at=!0)},o(lt){g(Q.$$.fragment,lt),g(ct.$$.fragment,lt),at=!1},d(lt){lt&&m(Z),M(Q),M(ct)}}}(r),w=(r[11]&&r[6].size>0||!r[11]&&r[23].size>0)&&r[57]===0&&Ns(Pe(r)),E=dt(r[55].sections||[]),O=[];for(let T=0;T<E.length;T+=1)O[T]=Ts(Bs(r,E,T));const S=T=>g(O[T],1,1,()=>{O[T]=null});return{c(){t=b("div"),e=b("div"),n=b("div"),s=b("h5"),k.c(),i=I(),o=b("div"),a.c(),c=I(),F&&F.c(),u=I(),w&&w.c(),d=I();for(let T=0;T<O.length;T+=1)O[T].c();p=I(),v(s,"class","c-diff-view__title svelte-ibi4q5"),v(o,"class","c-diff-view__description svelte-ibi4q5"),v(n,"class","c-diff-view__content svelte-ibi4q5"),v(e,"class","c-diff-view__header svelte-ibi4q5"),v(t,"class","c-diff-view__section svelte-ibi4q5"),v(t,"id",`section-${r[57]}`)},m(T,Z){h(T,t,Z),R(t,e),R(e,n),R(n,s),k.m(s,null),R(n,i),R(n,o),B[l].m(o,null),R(e,c),F&&F.m(e,null),R(t,u),w&&w.m(t,null),R(t,d);for(let Q=0;Q<O.length;Q+=1)O[Q]&&O[Q].m(t,null);R(t,p),C=!0},p(T,Z){$===($=x(T))&&k?k.p(T,Z):(k.d(1),k=$(T),k&&(k.c(),k.m(s,null)));let Q=l;if(l=D(T),l===Q?B[l].p(T,Z):(j(),g(B[Q],1,1,()=>{B[Q]=null}),W(),a=B[l],a?a.p(T,Z):(a=B[l]=_[l](T),a.c()),f(a,1),a.m(o,null)),T[57]===0&&F.p(T,Z),(T[11]&&T[6].size>0||!T[11]&&T[23].size>0)&&T[57]===0?w?(w.p(Pe(T),Z),8390720&Z[0]&&f(w,1)):(w=Ns(Pe(T)),w.c(),f(w,1),w.m(t,d)):w&&(j(),g(w,1,1,()=>{w=null}),W()),1080624040&Z[0]|1&Z[1]){let nt;for(E=dt(T[55].sections||[]),nt=0;nt<E.length;nt+=1){const ct=Bs(T,E,nt);O[nt]?(O[nt].p(ct,Z),f(O[nt],1)):(O[nt]=Ts(ct),O[nt].c(),f(O[nt],1),O[nt].m(t,p))}for(j(),nt=E.length;nt<O.length;nt+=1)S(nt);W()}},i(T){if(!C){f(a),f(F),f(w);for(let Z=0;Z<E.length;Z+=1)f(O[Z]);C=!0}},o(T){g(a),g(F),g(w),O=O.filter(vi);for(let Z=0;Z<O.length;Z+=1)g(O[Z]);C=!1},d(T){T&&m(t),k.d(),B[l].d(),F&&F.d(),w&&w.d(),Mt(O,T)}}}function Eu(r){let t,e,n,s;return n=new fe({}),{c(){t=N(`Apply All
                  `),e=b("div"),z(n.$$.fragment),v(e,"class","c-diff-view__controls__icon svelte-ibi4q5")},m(i,o){h(i,t,o),h(i,e,o),L(n,e,null),s=!0},i(i){s||(f(n.$$.fragment,i),s=!0)},o(i){g(n.$$.fragment,i),s=!1},d(i){i&&(m(t),m(e)),M(n)}}}function _u(r){let t,e,n;return e=new $t({props:{size:2,$$slots:{default:[zu]},$$scope:{ctx:r}}}),{c(){t=b("div"),z(e.$$.fragment),v(t,"class","c-diff-view__applied svelte-ibi4q5")},m(s,i){h(s,t,i),L(e,t,null),n=!0},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e)}}}function Bu(r){let t,e,n,s,i;return e=new Me({props:{size:1,useCurrentColor:!0}}),s=new $t({props:{size:2,$$slots:{default:[Lu]},$$scope:{ctx:r}}}),{c(){t=b("div"),z(e.$$.fragment),n=I(),z(s.$$.fragment),v(t,"class","c-diff-view__applying svelte-ibi4q5")},m(o,l){h(o,t,l),L(e,t,null),R(t,n),L(s,t,null),i=!0},i(o){i||(f(e.$$.fragment,o),f(s.$$.fragment,o),i=!0)},o(o){g(e.$$.fragment,o),g(s.$$.fragment,o),i=!1},d(o){o&&m(t),M(e),M(s)}}}function zu(r){let t,e,n;return e=new Re({props:{iconName:"check"}}),{c(){t=N(`Applied
                      `),z(e.$$.fragment)},m(s,i){h(s,t,i),L(e,s,i),n=!0},p:st,i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&m(t),M(e,s)}}}function Lu(r){let t;return{c(){t=N("Applying...")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function Mu(r){let t,e,n,s;const i=[Bu,_u,Eu],o=[];function l(a,c){return a[10]?0:a[11]?1:2}return t=l(r),e=o[t]=i[t](r),{c(){e.c(),n=vt()},m(a,c){o[t].m(a,c),h(a,n,c),s=!0},p(a,c){let u=t;t=l(a),t!==u&&(j(),g(o[u],1,1,()=>{o[u]=null}),W(),e=o[t],e||(e=o[t]=i[t](a),e.c()),f(e,1),e.m(n.parentNode,n))},i(a){s||(f(e),s=!0)},o(a){g(e),s=!1},d(a){a&&m(n),o[t].d(a)}}}function Ru(r){let t,e;return t=new Tt({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[10]||r[11]||r[13].length>0||!r[12],$$slots:{default:[Mu]},$$scope:{ctx:r}}}),t.$on("click",r[32]),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};15360&s[0]&&(i.disabled=n[10]||n[11]||n[13].length>0||!n[12]),3072&s[0]|8&s[2]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function Ou(r){let t;return{c(){t=N("No files changed")},m(e,n){h(e,t,n)},d(e){e&&m(t)}}}function Nu(r){let t,e,n,s,i,o,l,a,c=r[20]&&Ls(r);const u=[nu,eu],d=[];function p($,k){return $[26]?0:1}function C($){r[48]($)}n=p(r),s=d[n]=u[n](r);let x={applyAllChanges:r[33]};return r[24]!==void 0&&(x.showModal=r[24]),o=new Yc({props:x}),bt.push(()=>ee(o,"showModal",C)),{c(){t=b("div"),c&&c.c(),e=I(),s.c(),i=I(),z(o.$$.fragment),v(t,"class","c-diff-view svelte-ibi4q5")},m($,k){h($,t,k),c&&c.m(t,null),R(t,e),d[n].m(t,null),h($,i,k),L(o,$,k),a=!0},p($,k){$[20]?c?(c.p($,k),1048576&k[0]&&f(c,1)):(c=Ls($),c.c(),f(c,1),c.m(t,e)):c&&(j(),g(c,1,1,()=>{c=null}),W());let _=n;n=p($),n===_?d[n].p($,k):(j(),g(d[_],1,1,()=>{d[_]=null}),W(),s=d[n],s?s.p($,k):(s=d[n]=u[n]($),s.c()),f(s,1),s.m(t,null));const B={};!l&&16777216&k[0]&&(l=!0,B.showModal=$[24],ne(()=>l=!1)),o.$set(B)},i($){a||(f(c),f(s),f(o.$$.fragment,$),a=!0)},o($){g(c),g(s),g(o.$$.fragment,$),a=!1},d($){$&&(m(t),m(i)),c&&c.d(),d[n].d(),M(o,$)}}}function qu(r,t,e){let n,s,i,o,l,a,c,u,d,{changedFiles:p}=t,{agentLabel:C}=t,{latestUserPrompt:x}=t,{onApplyChanges:$}=t,{onOpenFile:k}=t,{onRenderBackup:_}=t,{preloadedExplanation:B}=t,{isAgentFromDifferentRepo:D=!1}=t,{conflictFiles:F=new Set}=t;const w=te(oe.key);let E="",O=!1,S=[],T=[],Z=!1,Q=!1,nt=null,ct=!0,at={},lt=[],J=!1,ft=!1,mt=!0,it=new Set,At=!1;const yt=Kt({});Rt(r,yt,y=>e(16,d=y));let Ct={};function Ot(y,q){e(38,Ct[y]=q,Ct)}async function pt(y,q,V){if($)return yt.update(P=>(P[y]="pending",P)),new Promise(P=>{$==null||$(y,q,V).then(()=>{yt.update(H=>(H[y]="applied",H)),P()})})}function Et(){if(!$)return;w.reportApplyChangesEvent(),e(10,J=!0),e(11,ft=!1);const{filesToApply:y,areAllPathsApplied:q}=an(S,p,Ct);q||y.length===0?e(11,ft=q):zi(y,pt).then(()=>{e(10,J=!1),e(11,ft=!0)})}function kt(y){const q={title:"Changed Files",description:`${y.length} files were changed`,sections:[]},V=[],P=[],H=[];return y.forEach(Y=>{Y.old_path?Y.new_path?P.push(Y):H.push(Y):V.push(Y)}),V.length>0&&q.sections.push(U("Added files","feature",V)),P.length>0&&q.sections.push(U("Modified files","fix",P)),H.length>0&&q.sections.push(U("Deleted files","chore",H)),[q]}function U(y,q,V){const P=[];return V.forEach(H=>{const Y=H.new_path||H.old_path,X=H.old_contents||"",G=H.new_contents||"",ut=H.old_path?H.old_path:"",ht=ye(ut,H.new_path||"/dev/null",X,G,"","",{context:3}),wt=`${Zt(Y)}-${Zt(X+G)}`;P.push({id:wt,path:Y,diff:ht,originalCode:X,modifiedCode:G})}),{title:y,descriptions:[],type:q,changes:P}}async function Jt(){if(!O)return;if(e(18,Z=!0),e(19,Q=!1),e(20,nt=null),e(17,T=[]),e(7,S=[]),l)return void e(18,Z=!1);const y=102400;let q=0;if(p.forEach(V=>{var P,H;q+=(((P=V.old_contents)==null?void 0:P.length)||0)+(((H=V.new_contents)==null?void 0:H.length)||0)}),p.length>12||q>512e3){try{e(7,S=kt(p))}catch(V){console.error("Failed to create simple explanation:",V),e(20,nt="Failed to create explanation for large changes.")}e(18,Z=!1)}else try{const V=new Gi(Y=>Xs.postMessage(Y)),P=new Map,H=p.map(Y=>{const X=Y.new_path||Y.old_path,G=Y.old_contents||"",ut=Y.new_contents||"",ht=`${Zt(X)}-${Zt(G+ut)}`;return P.set(ht,{old_path:Y.old_path,new_path:Y.new_path,old_contents:G,new_contents:ut,change_type:Y.change_type}),{id:ht,old_path:Y.old_path,new_path:Y.new_path,change_type:Y.change_type}});try{const Y=H.length===1;let X=[];Y?X=H.map(G=>({path:G.new_path||G.old_path,changes:[{id:G.id,path:G.new_path||G.old_path,diff:`File: ${G.new_path||G.old_path}
Change type: ${G.change_type||"modified"}`,originalCode:"",modifiedCode:""}]})):X=(await V.send({type:"get-diff-group-changes-request",data:{changedFiles:H,changesById:!0,apikey:E}},3e4)).data.groupedChanges,e(17,T=X.map(G=>({path:G.path,changes:G.changes.map(ut=>{if(ut.id&&P.has(ut.id)){const ht=P.get(ut.id);let wt=ut.diff;return wt&&!wt.startsWith("File:")||(wt=ye(ht.old_path||"",ht.new_path||"",ht.old_contents||"",ht.new_contents||"")),{...ut,diff:wt,old_path:ht.old_path,new_path:ht.new_path,old_contents:ht.old_contents,new_contents:ht.new_contents,change_type:ht.change_type,originalCode:ht.old_contents||"",modifiedCode:ht.new_contents||""}}return ut})})))}catch(Y){console.error("Failed to group changes with LLM, falling back to simple grouping:",Y);try{const X=H.map(G=>{if(G.id&&P.has(G.id)){const ut=P.get(G.id);return{...G,old_path:ut.old_path,new_path:ut.new_path,old_contents:ut.old_contents||"",new_contents:ut.new_contents||"",change_type:ut.change_type}}return G});e(7,S=kt(X)),e(17,T=S[0].sections.map(G=>({path:G.title,changes:G.changes}))),e(19,Q=!1)}catch(X){console.error("Failed to create simple explanation:",X),e(20,nt="Failed to group changes. Please try again.")}}if(e(18,Z=!1),!T||T.length===0)throw new Error("Failed to group changes");if(!S||S.length===0){e(7,S=function(X){const G={title:"Loading...",description:"",sections:[]};return X.forEach(ut=>{const ht=ut.changes.map(Pt=>{if(Pt.id)return Pt;const Wt=Zt(Pt.path),Xt=Zt(Pt.originalCode+Pt.modifiedCode);return{...Pt,id:`${Wt}-${Xt}`}}),wt={title:ut.path,descriptions:[],type:"other",changes:ht};G.sections.push(wt)}),[G]}(T));const Y=S[0].sections.map(X=>({path:X.title,changes:X.changes.map(G=>{var Pt,Wt,Xt;const ut=((Pt=G.originalCode)==null?void 0:Pt.length)||0,ht=((Wt=G.modifiedCode)==null?void 0:Wt.length)||0,wt=((Xt=G.diff)==null?void 0:Xt.length)||0;return ut>y||ht>y||wt>y?{id:G.id,path:G.path,diff:`File: ${G.path}
Content too large to include in explanation request (${Math.max(ut,ht,wt)} bytes)`,originalCode:ut>y?`[File content too large: ${ut} bytes]`:G.originalCode,modifiedCode:ht>y?`[File content too large: ${ht} bytes]`:G.modifiedCode}:{id:G.id,path:G.path,diff:G.diff,originalCode:G.originalCode,modifiedCode:G.modifiedCode}})}));e(19,Q=!0);try{const{explanation:X,error:G}=await w.getDescriptions(Y,E);if(G==="Token limit exceeded")return e(7,S=kt(p)),e(18,Z=!1),void e(19,Q=!1);X&&X.length>0&&X.forEach((ut,ht)=>{ut.sections&&ut.sections.forEach((wt,Pt)=>{wt.changes&&wt.changes.forEach(Wt=>{const Xt=S[ht];if(Xt&&Xt.sections){const qe=Xt.sections[Pt];if(qe&&qe.changes){const me=qe.changes.find(yi=>yi.id===Wt.id);me&&(Wt.originalCode=me.originalCode,Wt.modifiedCode=me.modifiedCode,Wt.diff=me.diff)}}})})}),e(7,S=X)}catch(X){console.error("Failed to get descriptions, using skeleton explanation:",X)}}S.length===0&&e(20,nt="Failed to generate explanation.")}catch(V){console.error("Failed to get explanation:",V),e(20,nt=V instanceof Error?V.message:"An error occurred while generating the explanation.")}finally{e(18,Z=!1),e(19,Q=!1)}}Gt(()=>{const y=localStorage.getItem("anthropic_apikey");y&&(E=y),e(37,O=!0)});let Yt="",A="Apply all changes locally";return r.$$set=y=>{"changedFiles"in y&&e(0,p=y.changedFiles),"agentLabel"in y&&e(1,C=y.agentLabel),"latestUserPrompt"in y&&e(2,x=y.latestUserPrompt),"onApplyChanges"in y&&e(35,$=y.onApplyChanges),"onOpenFile"in y&&e(3,k=y.onOpenFile),"onRenderBackup"in y&&e(4,_=y.onRenderBackup),"preloadedExplanation"in y&&e(36,B=y.preloadedExplanation),"isAgentFromDifferentRepo"in y&&e(5,D=y.isAgentFromDifferentRepo),"conflictFiles"in y&&e(6,F=y.conflictFiles)},r.$$.update=()=>{if(65537&r.$$.dirty[0]&&p&&yt.set(p.reduce((y,q)=>{const V=q.new_path||q.old_path;return y[V]=d[V]??"none",y},{})),1&r.$$.dirty[0]&&e(41,a=JSON.stringify(p)),1376&r.$$.dirty[1]&&O&&a&&a!==Yt&&(e(39,Yt=a),B&&B.length>0?(e(7,S=B),e(18,Z=!1),e(19,Q=!1)):Jt(),e(10,J=!1),e(11,ft=!1),e(38,Ct={})),896&r.$$.dirty[0]&&S&&S.length>0){const y=Fe(S);Array.from(y).forEach(P=>{at[P]===void 0&&e(9,at[P]=!ct,at)});const q=Object.keys(at).filter(P=>at[P]),V=Array.from(y);V.length>0&&e(8,ct=!V.some(P=>q.includes(P)))}if(512&r.$$.dirty[0]&&e(27,n=Object.values(at).some(Boolean)),128&r.$$.dirty[0]|128&r.$$.dirty[1]&&S&&S.length>0&&S.flatMap(y=>y.sections||[]).flatMap(y=>y.changes).forEach(y=>{Ct[y.path]||e(38,Ct[y.path]=y.modifiedCode,Ct)}),128&r.$$.dirty[0]&&e(40,s=JSON.stringify(S)),65664&r.$$.dirty[0]|512&r.$$.dirty[1]&&e(12,i=(()=>{if(s&&d){const y=Fe(S);return y.size!==0&&Array.from(y).some(q=>d[q]!=="applied")}return!1})()),65536&r.$$.dirty[0]&&e(11,ft=Object.keys(d).every(y=>d[y]==="applied")),65536&r.$$.dirty[0]&&e(13,o=Object.keys(d).filter(y=>d[y]==="pending")),129&r.$$.dirty[0]|128&r.$$.dirty[1]&&async function(y,q,V){const{filesToApply:P}=an(y,q,V),H=new Set;for(const Y of P)(await w.previewApplyChanges(Y.path,Y.originalCode,Y.newCode)).hasConflicts&&H.add(Y.path);e(23,it=H)}(S,p,Ct),1&r.$$.dirty[0]&&e(26,l=p.length===0),67712&r.$$.dirty[0]|512&r.$$.dirty[1]&&s&&ft){const y=Fe(S);Array.from(y).every(q=>d[q]==="applied")||e(11,ft=!1)}2112&r.$$.dirty[0]&&e(14,c=ft&&F.size>0),15392&r.$$.dirty[0]&&e(15,u=D||J||ft||o.length>0||!i),64544&r.$$.dirty[0]&&(u?D?e(25,A="Cannot apply changes from a different repository locally"):J?e(25,A="Applying changes..."):c?e(25,A="All changes applied, but conflicts need to be resolved manually"):ft?e(25,A="All changes applied"):o.length>0?e(25,A="Waiting for changes to apply"):i||e(25,A="No changes to apply"):e(25,A="Apply all changes locally"))},[p,C,x,k,_,D,F,S,ct,at,J,ft,i,o,c,u,d,T,Z,Q,nt,lt,mt,it,At,A,l,n,yt,function(){const y=Fe(S),q=Object.values(at).some(Boolean);e(8,ct=q),Array.from(y).forEach(V=>{e(9,at[V]=!ct,at)})},Ot,pt,async function(){const y=await w.canApplyChanges();y.canApply?Et():y.hasUnstagedChanges&&e(24,At=!0)},Et,Jt,$,B,O,Ct,Yt,s,a,(y,q)=>{Ot(y.path,q)},y=>{pt(y.path,y.originalCode,y.modifiedCode)},y=>k(y.path),function(y,q){r.$$.not_equal(at[q.path],y)&&(at[q.path]=y,e(9,at),e(7,S),e(8,ct),e(37,O),e(41,a),e(39,Yt),e(36,B),e(0,p))},function(y,q,V,P){bt[y?"unshift":"push"](()=>{lt[100*q+10*V+P.path.length%10]=y,e(21,lt)})},function(y){mt=y,e(22,mt)},function(y){At=y,e(24,At)}]}class Su extends tt{constructor(t){super(),et(this,t,qu,Nu,K,{changedFiles:0,agentLabel:1,latestUserPrompt:2,onApplyChanges:35,onOpenFile:3,onRenderBackup:4,preloadedExplanation:36,isAgentFromDifferentRepo:5,conflictFiles:6},null,[-1,-1,-1])}}function Is(r){let t,e,n=r[8].opts,s=Us(r);return{c(){t=b("div"),s.c(),v(t,"class","file-explorer-contents svelte-5tfpo4")},m(i,o){h(i,t,o),s.m(t,null),e=!0},p(i,o){256&o&&K(n,n=i[8].opts)?(j(),g(s,1,1,st),W(),s=Us(i),s.c(),f(s,1),s.m(t,null)):s.p(i,o)},i(i){e||(f(s),e=!0)},o(i){g(s),e=!1},d(i){i&&m(t),s.d(i)}}}function Tu(r){var n,s;let t,e;return t=new Su({props:{changedFiles:r[0],onApplyChanges:r[10],onOpenFile:r[11],agentLabel:r[3],latestUserPrompt:r[4],onRenderBackup:r[12],preloadedExplanation:(s=(n=r[8])==null?void 0:n.opts)==null?void 0:s.preloadedExplanation,isAgentFromDifferentRepo:r[5],conflictFiles:r[6]}}),{c(){z(t.$$.fragment)},m(i,o){L(t,i,o),e=!0},p(i,o){var a,c;const l={};1&o&&(l.changedFiles=i[0]),8&o&&(l.agentLabel=i[3]),16&o&&(l.latestUserPrompt=i[4]),128&o&&(l.onRenderBackup=i[12]),256&o&&(l.preloadedExplanation=(c=(a=i[8])==null?void 0:a.opts)==null?void 0:c.preloadedExplanation),32&o&&(l.isAgentFromDifferentRepo=i[5]),64&o&&(l.conflictFiles=i[6]),t.$set(l)},i(i){e||(f(t.$$.fragment,i),e=!0)},o(i){g(t.$$.fragment,i),e=!1},d(i){M(t,i)}}}function Pu(r){let t,e;return t=new Co({props:{changedFiles:r[0],onApplyChanges:r[10],onOpenFile:r[11],pendingFiles:r[1],appliedFiles:r[2]}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};1&s&&(i.changedFiles=n[0]),2&s&&(i.pendingFiles=n[1]),4&s&&(i.appliedFiles=n[2]),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function Us(r){let t,e,n,s;const i=[Pu,Tu],o=[];function l(a,c){return a[7]==="changedFiles"?0:1}return t=l(r),e=o[t]=i[t](r),{c(){e.c(),n=vt()},m(a,c){o[t].m(a,c),h(a,n,c),s=!0},p(a,c){let u=t;t=l(a),t===u?o[t].p(a,c):(j(),g(o[u],1,1,()=>{o[u]=null}),W(),e=o[t],e?e.p(a,c):(e=o[t]=i[t](a),e.c()),f(e,1),e.m(n.parentNode,n))},i(a){s||(f(e),s=!0)},o(a){g(e),s=!1},d(a){a&&m(n),o[t].d(a)}}}function Iu(r){let t,e,n,s=r[0]&&Is(r);return{c(){t=b("div"),e=b("div"),s&&s.c(),v(e,"class","file-explorer-main svelte-5tfpo4"),v(t,"class","diff-page svelte-5tfpo4")},m(i,o){h(i,t,o),R(t,e),s&&s.m(e,null),n=!0},p(i,[o]){i[0]?s?(s.p(i,o),1&o&&f(s,1)):(s=Is(i),s.c(),f(s,1),s.m(e,null)):s&&(j(),g(s,1,1,()=>{s=null}),W())},i(i){n||(f(s),n=!0)},o(i){g(s),n=!1},d(i){i&&m(t),s&&s.d()}}}function Uu(r,t,e){let n,{changedFiles:s=[]}=t,{pendingFiles:i=[]}=t,{appliedFiles:o=[]}=t,{agentLabel:l}=t,{latestUserPrompt:a}=t,{isAgentFromDifferentRepo:c=!1}=t,u=new Set;const d=te(oe.key),p=te(Ae.key);Rt(r,p,x=>e(8,n=x));let C="summary";return function(x){x.subscribe($=>{if($){const k=document.getElementById(Ve($));k&&k.scrollIntoView({behavior:"smooth",block:"center"})}})}(function(x=null){const $=Kt(x);return Ie(di,$),$}(null)),r.$$set=x=>{"changedFiles"in x&&e(0,s=x.changedFiles),"pendingFiles"in x&&e(1,i=x.pendingFiles),"appliedFiles"in x&&e(2,o=x.appliedFiles),"agentLabel"in x&&e(3,l=x.agentLabel),"latestUserPrompt"in x&&e(4,a=x.latestUserPrompt),"isAgentFromDifferentRepo"in x&&e(5,c=x.isAgentFromDifferentRepo)},[s,i,o,l,a,c,u,C,n,p,async(x,$,k)=>{const{success:_,hasConflicts:B}=await d.applyChanges(x,$,k);_&&B&&e(6,u=new Set([...u,x]))},x=>d.openFile(x),()=>{e(7,C="changedFiles")}]}class Vu extends tt{constructor(t){super(),et(this,t,Uu,Iu,K,{changedFiles:0,pendingFiles:1,appliedFiles:2,agentLabel:3,latestUserPrompt:4,isAgentFromDifferentRepo:5})}}function Zu(r){let t,e,n,s,i;return e=new Me({props:{size:1}}),{c(){t=b("div"),z(e.$$.fragment),n=I(),s=b("p"),s.textContent="Loading diff view...",v(t,"class","l-center svelte-ccste2")},m(o,l){h(o,t,l),L(e,t,null),R(t,n),R(t,s),i=!0},p:st,i(o){i||(f(e.$$.fragment,o),i=!0)},o(o){g(e.$$.fragment,o),i=!1},d(o){o&&m(t),M(e)}}}function Hu(r){let t,e;return t=new Vu({props:{changedFiles:r[0].changedFiles,agentLabel:r[0].sessionSummary,latestUserPrompt:r[0].userPrompt,pendingFiles:r[3].applyingFilePaths||[],appliedFiles:r[3].appliedFilePaths||[],isAgentFromDifferentRepo:r[0].isAgentFromDifferentRepo||!1}}),{c(){z(t.$$.fragment)},m(n,s){L(t,n,s),e=!0},p(n,s){const i={};1&s&&(i.changedFiles=n[0].changedFiles),1&s&&(i.agentLabel=n[0].sessionSummary),1&s&&(i.latestUserPrompt=n[0].userPrompt),1&s&&(i.isAgentFromDifferentRepo=n[0].isAgentFromDifferentRepo||!1),t.$set(i)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){g(t.$$.fragment,n),e=!1},d(n){M(t,n)}}}function ju(r){let t,e,n,s;const i=[Hu,Zu],o=[];function l(a,c){return a[0]?0:1}return e=l(r),n=o[e]=i[e](r),{c(){t=b("div"),n.c(),v(t,"class","l-main svelte-ccste2")},m(a,c){h(a,t,c),o[e].m(t,null),s=!0},p(a,c){let u=e;e=l(a),e===u?o[e].p(a,c):(j(),g(o[u],1,1,()=>{o[u]=null}),W(),n=o[e],n?n.p(a,c):(n=o[e]=i[e](a),n.c()),f(n,1),n.m(t,null))},i(a){s||(f(n),s=!0)},o(a){g(n),s=!1},d(a){a&&m(t),o[e].d()}}}function Wu(r){let t,e,n,s;return t=new Vi.Root({props:{$$slots:{default:[ju]},$$scope:{ctx:r}}}),{c(){z(t.$$.fragment)},m(i,o){L(t,i,o),e=!0,n||(s=Qt(window,"message",r[1].onMessageFromExtension),n=!0)},p(i,[o]){const l={};33&o&&(l.$$scope={dirty:o,ctx:i}),t.$set(l)},i(i){e||(f(t.$$.fragment,i),e=!0)},o(i){g(t.$$.fragment,i),e=!1},d(i){M(t,i),n=!1,s()}}}function Qu(r,t,e){let n,s,i=new Mi(Xs),o=new Ae(i);Rt(r,o,a=>e(4,s=a)),i.registerConsumer(o);let l=new oe(i);return Ie(oe.key,l),Ie(Ae.key,o),Gt(()=>(o.onPanelLoaded(),()=>{i.dispose()})),r.$$.update=()=>{16&r.$$.dirty&&e(0,n=s.opts)},[n,i,o,l,s]}new class extends tt{constructor(r){super(),et(this,r,Qu,Wu,K,{})}}({target:document.getElementById("app")});
