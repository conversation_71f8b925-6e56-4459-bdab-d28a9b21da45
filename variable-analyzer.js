#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 高级变量名分析工具
 * 功能：
 * 1. 分析变量名模式和频率
 * 2. 识别可能的有意义变量名
 * 3. 检测混淆模式
 * 4. 生成变量名映射建议
 */

class VariableAnalyzer {
  constructor() {
    this.variables = new Map();
    this.patterns = {
      // 常见的混淆模式
      obfuscated: [
        /^[a-zA-Z]\d+[a-zA-Z]$/,  // 如: a1b, x2y
        /^[a-zA-Z]{1,3}\d+$/,     // 如: a1, ab2, xyz123
        /^_0x[a-fA-F0-9]+$/,      // 如: _0x1a2b
        /^[A-Z]{2,4}[a-z]?$/,     // 如: MLt, LW, FLt
        /^[a-z][A-Z]\d*[a-z]?$/,  // 如: aB, xY1z
      ],
      // 可能有意义的变量名
      meaningful: [
        /^[a-z][a-zA-Z]*[a-z]$/,     // 驼峰命名
        /^[a-z]+_[a-z]+$/,           // 下划线命名
        /^[A-Z][a-z]+[A-Z][a-z]+$/,  // 帕斯卡命名
        /^(get|set|is|has|can|should|will)[A-Z]/,  // 方法前缀
        /^(on|handle|process|create|update|delete)[A-Z]/, // 事件处理
      ],
      // 常见的JavaScript内置对象和方法
      builtin: [
        /^(Object|Array|String|Number|Boolean|Function|Date|RegExp|Error)$/,
        /^(console|window|document|global|process|require|module|exports)$/,
        /^(setTimeout|setInterval|clearTimeout|clearInterval)$/,
        /^(JSON|Math|parseInt|parseFloat|isNaN|isFinite)$/,
      ]
    };
  }

  analyzeFile(filePath) {
    console.log(`\n🔍 开始分析文件: ${path.basename(filePath)}`);
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 提取所有变量名
    this.extractVariables(content);
    
    // 分类变量
    const classification = this.classifyVariables();
    
    // 生成报告
    const report = this.generateReport(classification, filePath);
    
    // 保存分析结果
    this.saveAnalysis(report, filePath);
    
    return report;
  }

  extractVariables(content) {
    // 匹配各种变量声明和使用模式
    const patterns = [
      /\b(?:var|let|const)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,  // 变量声明
      /\bfunction\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,           // 函数声明
      /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*[:=]/g,                 // 对象属性和赋值
      /\.([a-zA-Z_$][a-zA-Z0-9_$]*)/g,                      // 属性访问
      /\[['"]([a-zA-Z_$][a-zA-Z0-9_$]*)['"]]/g,            // 括号属性访问
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const varName = match[1];
        if (varName && varName.length > 0) {
          if (!this.variables.has(varName)) {
            this.variables.set(varName, {
              name: varName,
              count: 0,
              length: varName.length,
              type: 'unknown',
              contexts: []
            });
          }
          this.variables.get(varName).count++;
        }
      }
    });
  }

  classifyVariables() {
    const classification = {
      obfuscated: [],
      meaningful: [],
      builtin: [],
      short: [],
      medium: [],
      long: [],
      frequent: [],
      rare: []
    };

    for (const [name, info] of this.variables) {
      // 按模式分类
      if (this.matchesPatterns(name, this.patterns.builtin)) {
        info.type = 'builtin';
        classification.builtin.push(info);
      } else if (this.matchesPatterns(name, this.patterns.obfuscated)) {
        info.type = 'obfuscated';
        classification.obfuscated.push(info);
      } else if (this.matchesPatterns(name, this.patterns.meaningful)) {
        info.type = 'meaningful';
        classification.meaningful.push(info);
      }

      // 按长度分类
      if (name.length <= 3) {
        classification.short.push(info);
      } else if (name.length <= 8) {
        classification.medium.push(info);
      } else {
        classification.long.push(info);
      }

      // 按频率分类
      if (info.count >= 10) {
        classification.frequent.push(info);
      } else if (info.count <= 2) {
        classification.rare.push(info);
      }
    }

    // 排序
    Object.keys(classification).forEach(key => {
      classification[key].sort((a, b) => b.count - a.count);
    });

    return classification;
  }

  matchesPatterns(name, patterns) {
    return patterns.some(pattern => pattern.test(name));
  }

  generateReport(classification, filePath) {
    const totalVars = this.variables.size;
    
    const report = {
      file: path.basename(filePath),
      timestamp: new Date().toISOString(),
      summary: {
        totalVariables: totalVars,
        obfuscatedCount: classification.obfuscated.length,
        meaningfulCount: classification.meaningful.length,
        builtinCount: classification.builtin.length,
        obfuscationRatio: (classification.obfuscated.length / totalVars * 100).toFixed(2)
      },
      topObfuscated: classification.obfuscated.slice(0, 20),
      topMeaningful: classification.meaningful.slice(0, 20),
      topFrequent: classification.frequent.slice(0, 15),
      suspiciousPatterns: this.findSuspiciousPatterns(classification),
      recommendations: this.generateRecommendations(classification)
    };

    return report;
  }

  findSuspiciousPatterns(classification) {
    const patterns = [];
    
    // 查找高频短变量名（可能是重要的混淆变量）
    const suspiciousShort = classification.short
      .filter(v => v.count > 50 && v.length <= 2)
      .slice(0, 10);
    
    if (suspiciousShort.length > 0) {
      patterns.push({
        type: '高频短变量名',
        description: '这些变量名很短但使用频率很高，可能是重要的混淆变量',
        variables: suspiciousShort
      });
    }

    // 查找相似的变量名模式
    const similarPatterns = this.findSimilarPatterns();
    if (similarPatterns.length > 0) {
      patterns.push({
        type: '相似变量名模式',
        description: '这些变量名遵循相似的命名模式，可能来自同一个混淆器',
        patterns: similarPatterns
      });
    }

    return patterns;
  }

  findSimilarPatterns() {
    const patternGroups = new Map();
    
    for (const [name] of this.variables) {
      // 生成模式（用X替换字母，用N替换数字）
      const pattern = name.replace(/[a-zA-Z]/g, 'X').replace(/[0-9]/g, 'N');
      
      if (!patternGroups.has(pattern)) {
        patternGroups.set(pattern, []);
      }
      patternGroups.get(pattern).push(name);
    }

    // 返回有多个变量的模式
    return Array.from(patternGroups.entries())
      .filter(([pattern, names]) => names.length >= 3)
      .map(([pattern, names]) => ({ pattern, count: names.length, examples: names.slice(0, 5) }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  generateRecommendations(classification) {
    const recommendations = [];

    if (classification.obfuscated.length > 100) {
      recommendations.push({
        priority: 'high',
        type: '变量名反混淆',
        description: `发现${classification.obfuscated.length}个混淆变量名，建议使用专业的反混淆工具进行处理`
      });
    }

    if (classification.frequent.length > 0) {
      recommendations.push({
        priority: 'medium',
        type: '高频变量分析',
        description: `重点关注${classification.frequent.length}个高频变量，它们可能是核心功能的关键变量`
      });
    }

    const shortHighFreq = classification.short.filter(v => v.count > 20).length;
    if (shortHighFreq > 10) {
      recommendations.push({
        priority: 'high',
        type: '短变量名替换',
        description: `${shortHighFreq}个短变量名使用频率很高，建议优先进行有意义的重命名`
      });
    }

    return recommendations;
  }

  saveAnalysis(report, filePath) {
    const outputPath = filePath.replace('.js', '-variable-analysis.json');
    fs.writeFileSync(outputPath, JSON.stringify(report, null, 2), 'utf8');
    
    // 同时生成可读的文本报告
    const textReport = this.generateTextReport(report);
    const textOutputPath = filePath.replace('.js', '-variable-analysis.txt');
    fs.writeFileSync(textOutputPath, textReport, 'utf8');
    
    console.log(`📊 分析报告已保存:`);
    console.log(`   JSON格式: ${path.basename(outputPath)}`);
    console.log(`   文本格式: ${path.basename(textOutputPath)}`);
  }

  generateTextReport(report) {
    let text = `变量名分析报告\n`;
    text += `==================\n\n`;
    text += `文件: ${report.file}\n`;
    text += `分析时间: ${report.timestamp}\n\n`;
    
    text += `📈 总体统计:\n`;
    text += `- 总变量数: ${report.summary.totalVariables}\n`;
    text += `- 混淆变量: ${report.summary.obfuscatedCount} (${report.summary.obfuscationRatio}%)\n`;
    text += `- 有意义变量: ${report.summary.meaningfulCount}\n`;
    text += `- 内置变量: ${report.summary.builtinCount}\n\n`;
    
    if (report.topObfuscated.length > 0) {
      text += `🔍 主要混淆变量 (前20个):\n`;
      report.topObfuscated.forEach((v, i) => {
        text += `${i+1}. ${v.name} (使用${v.count}次)\n`;
      });
      text += `\n`;
    }
    
    if (report.topMeaningful.length > 0) {
      text += `✅ 有意义的变量 (前20个):\n`;
      report.topMeaningful.forEach((v, i) => {
        text += `${i+1}. ${v.name} (使用${v.count}次)\n`;
      });
      text += `\n`;
    }
    
    if (report.recommendations.length > 0) {
      text += `💡 处理建议:\n`;
      report.recommendations.forEach((rec, i) => {
        text += `${i+1}. [${rec.priority.toUpperCase()}] ${rec.type}: ${rec.description}\n`;
      });
    }
    
    return text;
  }
}

// 主程序
if (require.main === module) {
  const args = process.argv.slice(2);
  if (args.length === 0) {
    console.log('用法: node variable-analyzer.js <文件路径>');
    process.exit(1);
  }
  
  const analyzer = new VariableAnalyzer();
  
  args.forEach(filePath => {
    if (!fs.existsSync(filePath)) {
      console.error(`❌ 文件不存在: ${filePath}`);
      return;
    }
    
    try {
      analyzer.analyzeFile(filePath);
    } catch (error) {
      console.error(`❌ 分析文件时出错 (${filePath}):`, error.message);
    }
  });
}

module.exports = VariableAnalyzer;
