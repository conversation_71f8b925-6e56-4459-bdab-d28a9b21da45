#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

/**
 * 智能变量名替换工具
 * 基于变量分析结果，提供智能的变量名重命名建议和替换
 */

class SmartRenamer {
  constructor() {
    // 基于上下文的智能重命名映射
    this.contextMappings = {
      // 常见的单字母变量根据使用频率和上下文推测含义
      r: ['result', 'response', 'return', 'request', 'ref'],
      n: ['number', 'node', 'name', 'next', 'null'],
      t: ['type', 'target', 'temp', 'this', 'token'],
      s: ['string', 'source', 'state', 'self', 'size'],
      i: ['index', 'item', 'id', 'input', 'instance'],
      a: ['array', 'args', 'argument', 'attribute', 'action'],
      o: ['object', 'options', 'output', 'operation', 'owner'],
      e: ['element', 'event', 'error', 'entry', 'expression'],
      l: ['length', 'list', 'line', 'left', 'level'],
      c: ['count', 'config', 'context', 'callback', 'code'],

      // 常见的混淆模式
      MLt: 'ObjectCreate',
      LW: 'defineProperty',
      FLt: 'getOwnPropertyDescriptor',
      YLt: 'getOwnPropertyNames',
      NLt: 'getPrototypeOf',
      PLt: 'hasOwnProperty',
      Y1W: 'moduleLoader',
      O2p: 'exportHelper',
      L9p: 'requireHelper',
      P0d: 'propertyDefiner',
      q4L: 'scopeManager'
    }

    // 基于功能的重命名建议
    this.functionalMappings = {
      // 加密相关
      sha1: 'sha1Hash',
      sha512: 'sha512Hash',
      md5: 'md5Hash',
      asn1: 'asn1Parser',

      // 网络相关
      HTML: 'htmlContent',
      URL: 'urlHelper',
      DB: 'database',

      // 数据类型
      INT32: 'int32Type',
      ONE: 'constantOne',
      OMMM: 'objectMarker'
    }
  }

  analyzeAndRename(filePath, analysisPath) {
    console.log(`\n🔄 开始智能重命名: ${path.basename(filePath)}`)

    // 读取分析结果
    const analysis = JSON.parse(fs.readFileSync(analysisPath, 'utf8'))

    // 读取源文件
    let content = fs.readFileSync(filePath, 'utf8')

    // 生成重命名映射
    const renameMap = this.generateRenameMap(analysis)

    // 应用重命名
    const renamedContent = this.applyRenames(content, renameMap)

    // 生成重命名报告
    const report = this.generateRenameReport(renameMap, analysis)

    // 保存结果
    this.saveResults(renamedContent, report, filePath)

    return { content: renamedContent, report, renameMap }
  }

  generateRenameMap(analysis) {
    const renameMap = new Map()

    // 处理高频混淆变量
    const topObfuscated = analysis.topObfuscated.slice(0, 50) // 前50个最重要的

    topObfuscated.forEach(variable => {
      const oldName = variable.name
      let newName = null

      // 检查是否有预定义的映射
      if (this.contextMappings[oldName]) {
        if (Array.isArray(this.contextMappings[oldName])) {
          // 如果有多个选项，选择第一个作为默认
          newName = this.contextMappings[oldName][0]
        } else {
          newName = this.contextMappings[oldName]
        }
      } else if (this.functionalMappings[oldName]) {
        newName = this.functionalMappings[oldName]
      } else {
        // 生成基于模式的名称
        newName = this.generatePatternBasedName(oldName, variable)
      }

      if (newName && newName !== oldName) {
        renameMap.set(oldName, {
          newName,
          oldName,
          frequency: variable.count,
          confidence: this.calculateConfidence(oldName, newName, variable),
          reason: this.getRenameReason(oldName, newName)
        })
      }
    })

    // 处理可疑的短变量名
    if (analysis.suspiciousPatterns) {
      const shortVars = analysis.suspiciousPatterns.find(p => p.type === '高频短变量名')
      if (shortVars) {
        shortVars.variables.forEach(variable => {
          const oldName = variable.name
          if (!renameMap.has(oldName) && this.contextMappings[oldName]) {
            const suggestions = this.contextMappings[oldName]
            const newName = Array.isArray(suggestions) ? suggestions[0] : suggestions

            renameMap.set(oldName, {
              newName,
              oldName,
              frequency: variable.count,
              confidence: 0.7, // 中等置信度
              reason: '高频短变量名，基于上下文推测'
            })
          }
        })
      }
    }

    return renameMap
  }

  generatePatternBasedName(oldName, variable) {
    // 基于变量名模式生成新名称
    if (oldName.length <= 3 && /^[A-Z]+$/.test(oldName)) {
      return `constant_${oldName.toLowerCase()}`
    }

    if (oldName.length <= 3 && /^[a-z]+$/.test(oldName)) {
      return `var_${oldName}`
    }

    if (/^[A-Z][a-z]*[A-Z]/.test(oldName)) {
      return `helper_${oldName.toLowerCase()}`
    }

    if (/^\d/.test(oldName)) {
      return `num_${oldName}`
    }

    // 默认情况
    return `renamed_${oldName}`
  }

  calculateConfidence(oldName, newName, variable) {
    let confidence = 0.5 // 基础置信度

    // 如果有预定义映射，提高置信度
    if (this.contextMappings[oldName] || this.functionalMappings[oldName]) {
      confidence += 0.3
    }

    // 高频变量提高置信度
    if (variable.count > 1000) {
      confidence += 0.2
    } else if (variable.count > 100) {
      confidence += 0.1
    }

    // 短变量名降低置信度
    if (oldName.length === 1) {
      confidence -= 0.1
    }

    return Math.min(0.95, Math.max(0.1, confidence))
  }

  getRenameReason(oldName, newName) {
    if (this.contextMappings[oldName]) {
      return '基于上下文模式匹配'
    }
    if (this.functionalMappings[oldName]) {
      return '基于功能语义识别'
    }
    return '基于命名模式推测'
  }

  applyRenames(content, renameMap) {
    let renamedContent = content

    // 按照变量名长度排序，先替换长的，避免部分匹配问题
    const sortedRenames = Array.from(renameMap.entries()).sort(([a], [b]) => b.length - a.length)

    sortedRenames.forEach(([oldName, renameInfo]) => {
      if (renameInfo.confidence >= 0.6) {
        // 只应用高置信度的重命名
        // 对于单字母变量，使用更严格的匹配规则
        if (oldName.length === 1) {
          // 避免在正则表达式、字符串字面量等上下文中替换
          const safeRegex = new RegExp(
            `(?<![\\/'"\\[])\\b${this.escapeRegex(oldName)}\\b(?!['"\\]gi])`,
            'g'
          )
          renamedContent = renamedContent.replace(safeRegex, renameInfo.newName)
        } else {
          // 对于多字母变量，使用标准的词边界匹配
          const regex = new RegExp(`\\b${this.escapeRegex(oldName)}\\b`, 'g')
          renamedContent = renamedContent.replace(regex, renameInfo.newName)
        }
      }
    })

    return renamedContent
  }

  escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  generateRenameReport(renameMap, analysis) {
    const appliedRenames = Array.from(renameMap.values())
      .filter(r => r.confidence >= 0.6)
      .sort((a, b) => b.frequency - a.frequency)

    const skippedRenames = Array.from(renameMap.values())
      .filter(r => r.confidence < 0.6)
      .sort((a, b) => b.frequency - a.frequency)

    return {
      summary: {
        totalCandidates: renameMap.size,
        appliedRenames: appliedRenames.length,
        skippedRenames: skippedRenames.length,
        averageConfidence:
          appliedRenames.reduce((sum, r) => sum + r.confidence, 0) / appliedRenames.length || 0
      },
      appliedRenames: appliedRenames.slice(0, 30), // 前30个
      skippedRenames: skippedRenames.slice(0, 20), // 前20个
      recommendations: this.generateRecommendations(renameMap, analysis)
    }
  }

  generateRecommendations(renameMap, analysis) {
    const recommendations = []

    const highFreqSkipped = Array.from(renameMap.values()).filter(
      r => r.confidence < 0.6 && r.frequency > 100
    )

    if (highFreqSkipped.length > 0) {
      recommendations.push({
        type: '手动审查建议',
        priority: 'high',
        description: `${highFreqSkipped.length}个高频变量因置信度不足被跳过，建议手动审查`,
        variables: highFreqSkipped.slice(0, 10).map(r => r.oldName)
      })
    }

    const lowConfidenceApplied = Array.from(renameMap.values()).filter(
      r => r.confidence >= 0.6 && r.confidence < 0.8
    )

    if (lowConfidenceApplied.length > 0) {
      recommendations.push({
        type: '验证建议',
        priority: 'medium',
        description: `${lowConfidenceApplied.length}个变量重命名置信度较低，建议验证`,
        variables: lowConfidenceApplied.slice(0, 10).map(r => `${r.oldName} → ${r.newName}`)
      })
    }

    return recommendations
  }

  saveResults(content, report, filePath) {
    // 保存重命名后的文件
    const renamedPath = filePath.replace('.js', '-renamed.js')
    fs.writeFileSync(renamedPath, content, 'utf8')

    // 保存重命名报告
    const reportPath = filePath.replace('.js', '-rename-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8')

    // 生成可读的文本报告
    const textReport = this.generateTextReport(report)
    const textReportPath = filePath.replace('.js', '-rename-report.txt')
    fs.writeFileSync(textReportPath, textReport, 'utf8')

    console.log(`✅ 重命名完成:`)
    console.log(`   重命名文件: ${path.basename(renamedPath)}`)
    console.log(`   JSON报告: ${path.basename(reportPath)}`)
    console.log(`   文本报告: ${path.basename(textReportPath)}`)
    console.log(
      `📊 统计: 应用了${report.summary.appliedRenames}个重命名，跳过了${report.summary.skippedRenames}个`
    )
  }

  generateTextReport(report) {
    let text = `智能重命名报告\n`
    text += `==================\n\n`

    text += `📈 重命名统计:\n`
    text += `- 候选变量: ${report.summary.totalCandidates}\n`
    text += `- 已应用重命名: ${report.summary.appliedRenames}\n`
    text += `- 跳过重命名: ${report.summary.skippedRenames}\n`
    text += `- 平均置信度: ${(report.summary.averageConfidence * 100).toFixed(1)}%\n\n`

    if (report.appliedRenames.length > 0) {
      text += `✅ 已应用的重命名 (前20个):\n`
      report.appliedRenames.slice(0, 20).forEach((rename, i) => {
        text += `${i + 1}. ${rename.oldName} → ${rename.newName} (${rename.frequency}次, ${(rename.confidence * 100).toFixed(1)}%)\n`
        text += `   原因: ${rename.reason}\n`
      })
      text += `\n`
    }

    if (report.recommendations.length > 0) {
      text += `💡 建议:\n`
      report.recommendations.forEach((rec, i) => {
        text += `${i + 1}. [${rec.priority.toUpperCase()}] ${rec.type}: ${rec.description}\n`
        if (rec.variables) {
          text += `   涉及变量: ${rec.variables.join(', ')}\n`
        }
      })
    }

    return text
  }
}

// 主程序
if (require.main === module) {
  const args = process.argv.slice(2)
  if (args.length < 2) {
    console.log('用法: node smart-rename.js <源文件路径> <分析文件路径>')
    console.log(
      '例如: node smart-rename.js 513/extension-improved.js 513/extension-improved-variable-analysis.json'
    )
    process.exit(1)
  }

  const [filePath, analysisPath] = args

  if (!fs.existsSync(filePath)) {
    console.error(`❌ 源文件不存在: ${filePath}`)
    process.exit(1)
  }

  if (!fs.existsSync(analysisPath)) {
    console.error(`❌ 分析文件不存在: ${analysisPath}`)
    process.exit(1)
  }

  try {
    const renamer = new SmartRenamer()
    renamer.analyzeAndRename(filePath, analysisPath)
  } catch (error) {
    console.error('❌ 重命名过程中出错:', error.message)
    process.exit(1)
  }
}

module.exports = SmartRenamer
