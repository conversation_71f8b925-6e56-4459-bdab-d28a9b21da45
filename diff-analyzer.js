#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 差异对比分析工具
 * 分析两个处理后的扩展文件之间的差异
 */

class DiffAnalyzer {
  constructor() {
    this.differences = {
      variables: new Map(),
      functions: new Map(),
      strings: new Map(),
      patterns: new Map()
    };
  }

  analyzeDifferences(file1Path, file2Path, analysis1Path, analysis2Path) {
    console.log(`\n🔍 开始差异分析:`);
    console.log(`   文件1: ${path.basename(file1Path)}`);
    console.log(`   文件2: ${path.basename(file2Path)}`);
    
    // 读取文件内容
    const content1 = fs.readFileSync(file1Path, 'utf8');
    const content2 = fs.readFileSync(file2Path, 'utf8');
    
    // 读取分析结果
    const analysis1 = JSON.parse(fs.readFileSync(analysis1Path, 'utf8'));
    const analysis2 = JSON.parse(fs.readFileSync(analysis2Path, 'utf8'));
    
    // 执行各种差异分析
    const report = {
      summary: this.generateSummary(content1, content2, analysis1, analysis2),
      variableDifferences: this.analyzeVariableDifferences(analysis1, analysis2),
      contentDifferences: this.analyzeContentDifferences(content1, content2),
      structuralDifferences: this.analyzeStructuralDifferences(content1, content2),
      obfuscationDifferences: this.analyzeObfuscationDifferences(analysis1, analysis2),
      recommendations: this.generateRecommendations(analysis1, analysis2)
    };
    
    // 保存报告
    this.saveDiffReport(report, file1Path, file2Path);
    
    return report;
  }

  generateSummary(content1, content2, analysis1, analysis2) {
    return {
      file1: {
        size: content1.length,
        lines: content1.split('\n').length,
        variables: analysis1.summary.totalVariables,
        obfuscated: analysis1.summary.obfuscatedCount,
        obfuscationRatio: analysis1.summary.obfuscationRatio
      },
      file2: {
        size: content2.length,
        lines: content2.split('\n').length,
        variables: analysis2.summary.totalVariables,
        obfuscated: analysis2.summary.obfuscatedCount,
        obfuscationRatio: analysis2.summary.obfuscationRatio
      },
      differences: {
        sizeDiff: content2.length - content1.length,
        linesDiff: content2.split('\n').length - content1.split('\n').length,
        variablesDiff: analysis2.summary.totalVariables - analysis1.summary.totalVariables,
        obfuscatedDiff: analysis2.summary.obfuscatedCount - analysis1.summary.obfuscatedCount
      }
    };
  }

  analyzeVariableDifferences(analysis1, analysis2) {
    const vars1 = new Map(analysis1.topObfuscated.map(v => [v.name, v]));
    const vars2 = new Map(analysis2.topObfuscated.map(v => [v.name, v]));
    
    const onlyInFile1 = [];
    const onlyInFile2 = [];
    const common = [];
    const frequencyDifferences = [];

    // 找出只在文件1中的变量
    for (const [name, info] of vars1) {
      if (!vars2.has(name)) {
        onlyInFile1.push(info);
      }
    }

    // 找出只在文件2中的变量
    for (const [name, info] of vars2) {
      if (!vars1.has(name)) {
        onlyInFile2.push(info);
      }
    }

    // 找出共同变量和频率差异
    for (const [name, info1] of vars1) {
      if (vars2.has(name)) {
        const info2 = vars2.get(name);
        common.push({
          name,
          file1Count: info1.count,
          file2Count: info2.count,
          difference: info2.count - info1.count
        });
        
        if (Math.abs(info2.count - info1.count) > 10) {
          frequencyDifferences.push({
            name,
            file1Count: info1.count,
            file2Count: info2.count,
            difference: info2.count - info1.count,
            percentChange: ((info2.count - info1.count) / info1.count * 100).toFixed(2)
          });
        }
      }
    }

    return {
      onlyInFile1: onlyInFile1.sort((a, b) => b.count - a.count).slice(0, 20),
      onlyInFile2: onlyInFile2.sort((a, b) => b.count - a.count).slice(0, 20),
      common: common.sort((a, b) => Math.abs(b.difference) - Math.abs(a.difference)).slice(0, 20),
      frequencyDifferences: frequencyDifferences.sort((a, b) => Math.abs(b.difference) - Math.abs(a.difference))
    };
  }

  analyzeContentDifferences(content1, content2) {
    // 分析字符串字面量差异
    const strings1 = this.extractStrings(content1);
    const strings2 = this.extractStrings(content2);
    
    // 分析函数定义差异
    const functions1 = this.extractFunctions(content1);
    const functions2 = this.extractFunctions(content2);
    
    // 分析导入/导出差异
    const imports1 = this.extractImports(content1);
    const imports2 = this.extractImports(content2);

    return {
      strings: {
        file1Count: strings1.length,
        file2Count: strings2.length,
        uniqueToFile1: strings1.filter(s => !strings2.includes(s)).slice(0, 10),
        uniqueToFile2: strings2.filter(s => !strings1.includes(s)).slice(0, 10)
      },
      functions: {
        file1Count: functions1.length,
        file2Count: functions2.length,
        uniqueToFile1: functions1.filter(f => !functions2.includes(f)).slice(0, 10),
        uniqueToFile2: functions2.filter(f => !functions1.includes(f)).slice(0, 10)
      },
      imports: {
        file1Count: imports1.length,
        file2Count: imports2.length,
        uniqueToFile1: imports1.filter(i => !imports2.includes(i)),
        uniqueToFile2: imports2.filter(i => !imports1.includes(i))
      }
    };
  }

  extractStrings(content) {
    const stringRegex = /(['"`])((?:(?!\1)[^\\]|\\.)*)?\1/g;
    const strings = [];
    let match;
    
    while ((match = stringRegex.exec(content)) !== null) {
      if (match[2] && match[2].length > 3) { // 只关注长度大于3的字符串
        strings.push(match[2]);
      }
    }
    
    return [...new Set(strings)]; // 去重
  }

  extractFunctions(content) {
    const functionRegex = /function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g;
    const arrowFunctionRegex = /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*\(/g;
    const functions = [];
    let match;
    
    while ((match = functionRegex.exec(content)) !== null) {
      functions.push(match[1]);
    }
    
    while ((match = arrowFunctionRegex.exec(content)) !== null) {
      functions.push(match[1]);
    }
    
    return [...new Set(functions)];
  }

  extractImports(content) {
    const importRegex = /(?:import|require)\s*\(?['"`]([^'"`]+)['"`]\)?/g;
    const imports = [];
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      imports.push(match[1]);
    }
    
    return [...new Set(imports)];
  }

  analyzeStructuralDifferences(content1, content2) {
    // 分析代码结构差异
    const structure1 = this.analyzeStructure(content1);
    const structure2 = this.analyzeStructure(content2);

    return {
      file1: structure1,
      file2: structure2,
      differences: {
        blocksDiff: structure2.blocks - structure1.blocks,
        conditionalsDiff: structure2.conditionals - structure1.conditionals,
        loopsDiff: structure2.loops - structure1.loops,
        tryCatchDiff: structure2.tryCatch - structure1.tryCatch
      }
    };
  }

  analyzeStructure(content) {
    return {
      blocks: (content.match(/\{/g) || []).length,
      conditionals: (content.match(/\b(if|else|switch)\b/g) || []).length,
      loops: (content.match(/\b(for|while|do)\b/g) || []).length,
      tryCatch: (content.match(/\b(try|catch|finally)\b/g) || []).length,
      functions: (content.match(/\bfunction\b/g) || []).length,
      classes: (content.match(/\bclass\b/g) || []).length
    };
  }

  analyzeObfuscationDifferences(analysis1, analysis2) {
    // 分析混淆模式差异
    const patterns1 = analysis1.suspiciousPatterns || [];
    const patterns2 = analysis2.suspiciousPatterns || [];

    return {
      file1Patterns: patterns1.length,
      file2Patterns: patterns2.length,
      patternComparison: this.comparePatterns(patterns1, patterns2)
    };
  }

  comparePatterns(patterns1, patterns2) {
    const comparison = [];
    
    // 比较相似变量名模式
    const similar1 = patterns1.find(p => p.type === '相似变量名模式');
    const similar2 = patterns2.find(p => p.type === '相似变量名模式');
    
    if (similar1 && similar2) {
      comparison.push({
        type: '相似变量名模式',
        file1Count: similar1.patterns ? similar1.patterns.length : 0,
        file2Count: similar2.patterns ? similar2.patterns.length : 0,
        commonPatterns: this.findCommonPatterns(similar1.patterns || [], similar2.patterns || [])
      });
    }

    return comparison;
  }

  findCommonPatterns(patterns1, patterns2) {
    const common = [];
    
    patterns1.forEach(p1 => {
      const match = patterns2.find(p2 => p2.pattern === p1.pattern);
      if (match) {
        common.push({
          pattern: p1.pattern,
          file1Count: p1.count,
          file2Count: match.count,
          difference: match.count - p1.count
        });
      }
    });
    
    return common.sort((a, b) => Math.abs(b.difference) - Math.abs(a.difference));
  }

  generateRecommendations(analysis1, analysis2) {
    const recommendations = [];
    
    const obfuscationDiff = analysis2.summary.obfuscatedCount - analysis1.summary.obfuscatedCount;
    
    if (Math.abs(obfuscationDiff) > 100) {
      recommendations.push({
        type: '混淆程度差异',
        priority: 'high',
        description: `两个文件的混淆变量数量差异较大 (${obfuscationDiff})，建议重点分析差异原因`,
        action: '检查是否为不同版本或不同构建配置'
      });
    }

    const sizeDiff = Math.abs(analysis2.summary.totalVariables - analysis1.summary.totalVariables);
    if (sizeDiff > 1000) {
      recommendations.push({
        type: '代码规模差异',
        priority: 'medium',
        description: `两个文件的变量总数差异较大 (${sizeDiff})，可能包含不同的功能模块`,
        action: '分析独有变量以识别功能差异'
      });
    }

    recommendations.push({
      type: '对比分析建议',
      priority: 'info',
      description: '建议重点关注高频变量的使用差异，这可能反映了核心功能的变化',
      action: '优先分析频率差异超过100次的变量'
    });

    return recommendations;
  }

  saveDiffReport(report, file1Path, file2Path) {
    const reportPath = '513/extension-diff-analysis.json';
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
    
    const textReport = this.generateTextReport(report, file1Path, file2Path);
    const textReportPath = '513/extension-diff-analysis.txt';
    fs.writeFileSync(textReportPath, textReport, 'utf8');
    
    console.log(`\n✅ 差异分析完成:`);
    console.log(`   JSON报告: ${path.basename(reportPath)}`);
    console.log(`   文本报告: ${path.basename(textReportPath)}`);
  }

  generateTextReport(report, file1Path, file2Path) {
    let text = `扩展文件差异分析报告\n`;
    text += `========================\n\n`;
    text += `文件1: ${path.basename(file1Path)}\n`;
    text += `文件2: ${path.basename(file2Path)}\n`;
    text += `分析时间: ${new Date().toISOString()}\n\n`;
    
    text += `📊 基本统计对比:\n`;
    text += `文件1: ${report.summary.file1.size} bytes, ${report.summary.file1.lines} 行, ${report.summary.file1.variables} 变量\n`;
    text += `文件2: ${report.summary.file2.size} bytes, ${report.summary.file2.lines} 行, ${report.summary.file2.variables} 变量\n`;
    text += `差异: ${report.summary.differences.sizeDiff > 0 ? '+' : ''}${report.summary.differences.sizeDiff} bytes, `;
    text += `${report.summary.differences.linesDiff > 0 ? '+' : ''}${report.summary.differences.linesDiff} 行, `;
    text += `${report.summary.differences.variablesDiff > 0 ? '+' : ''}${report.summary.differences.variablesDiff} 变量\n\n`;
    
    text += `🔍 混淆程度对比:\n`;
    text += `文件1混淆变量: ${report.summary.file1.obfuscated} (${report.summary.file1.obfuscationRatio}%)\n`;
    text += `文件2混淆变量: ${report.summary.file2.obfuscated} (${report.summary.file2.obfuscationRatio}%)\n`;
    text += `混淆差异: ${report.summary.differences.obfuscatedDiff > 0 ? '+' : ''}${report.summary.differences.obfuscatedDiff}\n\n`;
    
    if (report.variableDifferences.onlyInFile1.length > 0) {
      text += `📝 仅在文件1中的变量 (前10个):\n`;
      report.variableDifferences.onlyInFile1.slice(0, 10).forEach((v, i) => {
        text += `${i+1}. ${v.name} (${v.count}次)\n`;
      });
      text += `\n`;
    }
    
    if (report.variableDifferences.onlyInFile2.length > 0) {
      text += `📝 仅在文件2中的变量 (前10个):\n`;
      report.variableDifferences.onlyInFile2.slice(0, 10).forEach((v, i) => {
        text += `${i+1}. ${v.name} (${v.count}次)\n`;
      });
      text += `\n`;
    }
    
    if (report.variableDifferences.frequencyDifferences.length > 0) {
      text += `📈 频率差异最大的变量 (前10个):\n`;
      report.variableDifferences.frequencyDifferences.slice(0, 10).forEach((v, i) => {
        text += `${i+1}. ${v.name}: ${v.file1Count} → ${v.file2Count} (${v.percentChange > 0 ? '+' : ''}${v.percentChange}%)\n`;
      });
      text += `\n`;
    }
    
    text += `💡 分析建议:\n`;
    report.recommendations.forEach((rec, i) => {
      text += `${i+1}. [${rec.priority.toUpperCase()}] ${rec.type}: ${rec.description}\n`;
      text += `   建议行动: ${rec.action}\n`;
    });
    
    return text;
  }
}

// 主程序
if (require.main === module) {
  const args = process.argv.slice(2);
  if (args.length < 4) {
    console.log('用法: node diff-analyzer.js <文件1路径> <文件2路径> <分析1路径> <分析2路径>');
    console.log('例如: node diff-analyzer.js 513/extension-improved-safe-renamed.js 513/extension-patched-improved-safe-renamed.js 513/extension-improved-variable-analysis.json 513/extension-patched-improved-variable-analysis.json');
    process.exit(1);
  }
  
  const [file1Path, file2Path, analysis1Path, analysis2Path] = args;
  
  // 检查文件存在性
  [file1Path, file2Path, analysis1Path, analysis2Path].forEach(filePath => {
    if (!fs.existsSync(filePath)) {
      console.error(`❌ 文件不存在: ${filePath}`);
      process.exit(1);
    }
  });
  
  try {
    const analyzer = new DiffAnalyzer();
    analyzer.analyzeDifferences(file1Path, file2Path, analysis1Path, analysis2Path);
  } catch (error) {
    console.error('❌ 差异分析过程中出错:', error.message);
    process.exit(1);
  }
}

module.exports = DiffAnalyzer;
