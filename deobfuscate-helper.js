#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 简单的JavaScript反混淆辅助工具
 * 主要功能：
 * 1. 识别和替换常见的混淆模式
 * 2. 美化代码结构
 * 3. 添加注释说明
 */

function analyzeAndImprove(filePath) {
  console.log(`正在分析文件: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  const originalSize = content.length;
  
  // 统计信息
  const stats = {
    originalLines: content.split('\n').length,
    originalSize: originalSize,
    shortVarNames: 0,
    longVarNames: 0,
    functions: 0,
    objects: 0
  };
  
  // 1. 识别短变量名（可能是混淆的）
  const shortVarPattern = /\b[a-zA-Z_$][a-zA-Z0-9_$]{0,2}\b/g;
  const shortVars = content.match(shortVarPattern) || [];
  stats.shortVarNames = new Set(shortVars).size;
  
  // 2. 识别长变量名（可能是有意义的）
  const longVarPattern = /\b[a-zA-Z_$][a-zA-Z0-9_$]{3,}\b/g;
  const longVars = content.match(longVarPattern) || [];
  stats.longVarNames = new Set(longVars).size;
  
  // 3. 统计函数数量
  const functionPattern = /function\s+[a-zA-Z_$][a-zA-Z0-9_$]*\s*\(/g;
  stats.functions = (content.match(functionPattern) || []).length;
  
  // 4. 统计对象数量
  const objectPattern = /\{[^}]*\}/g;
  stats.objects = (content.match(objectPattern) || []).length;
  
  // 5. 替换一些常见的混淆模式
  let improved = content;
  
  // 替换 !0 为 true, !1 为 false
  improved = improved.replace(/!0\b/g, 'true');
  improved = improved.replace(/!1\b/g, 'false');
  
  // 替换 void 0 为 undefined
  improved = improved.replace(/void 0/g, 'undefined');
  
  // 替换一些常见的数字表达式
  improved = improved.replace(/0x[0-9a-fA-F]+/g, (match) => {
    const decimal = parseInt(match, 16);
    return decimal.toString();
  });
  
  // 6. 添加一些有用的注释
  const header = `/*
 * 反混淆处理后的文件
 * 原始文件: ${path.basename(filePath)}
 * 处理时间: ${new Date().toISOString()}
 * 
 * 统计信息:
 * - 原始行数: ${stats.originalLines}
 * - 原始大小: ${stats.originalSize} bytes
 * - 短变量名数量: ${stats.shortVarNames}
 * - 长变量名数量: ${stats.longVarNames}
 * - 函数数量: ${stats.functions}
 * - 对象数量: ${stats.objects}
 * 
 * 注意: 此文件已经过基本的反混淆处理，但可能仍包含混淆的变量名
 */

`;
  
  improved = header + improved;
  
  // 7. 保存改进后的文件
  const outputPath = filePath.replace('.js', '-improved.js');
  fs.writeFileSync(outputPath, improved, 'utf8');
  
  console.log(`分析完成！`);
  console.log(`统计信息:`);
  console.log(`- 原始行数: ${stats.originalLines}`);
  console.log(`- 原始大小: ${stats.originalSize} bytes`);
  console.log(`- 短变量名数量: ${stats.shortVarNames}`);
  console.log(`- 长变量名数量: ${stats.longVarNames}`);
  console.log(`- 函数数量: ${stats.functions}`);
  console.log(`- 对象数量: ${stats.objects}`);
  console.log(`改进后的文件已保存为: ${outputPath}`);
  
  return stats;
}

// 主程序
if (require.main === module) {
  const args = process.argv.slice(2);
  if (args.length === 0) {
    console.log('用法: node deobfuscate-helper.js <文件路径>');
    process.exit(1);
  }
  
  const filePath = args[0];
  if (!fs.existsSync(filePath)) {
    console.error(`文件不存在: ${filePath}`);
    process.exit(1);
  }
  
  try {
    analyzeAndImprove(filePath);
  } catch (error) {
    console.error('处理文件时出错:', error.message);
    process.exit(1);
  }
}

module.exports = { analyzeAndImprove };
